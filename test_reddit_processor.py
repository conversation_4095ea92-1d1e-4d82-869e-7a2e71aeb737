#!/usr/bin/env python3
"""
Reddit转储处理器测试脚本

该脚本用于测试Reddit转储处理器的功能，包括：
- 配置验证
- 文件处理
- 数据过滤
- 输出格式
"""

import json
import tempfile
import unittest
from datetime import datetime, timezone
from pathlib import Path
from unittest.mock import patch

class TestRedditDumpProcessor(unittest.TestCase):
    """Reddit转储处理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)
        self.dump_dir = self.temp_path / "dumps"
        self.output_dir = self.temp_path / "output"
        
        # 创建目录
        self.dump_dir.mkdir(parents=True, exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_dump_file(self, filename: str, posts: list):
        """创建测试转储文件"""
        dump_file = self.dump_dir / filename
        with open(dump_file, 'w', encoding='utf-8') as f:
            for post in posts:
                f.write(json.dumps(post) + '\n')
        return dump_file
    
    def test_config_import(self):
        """测试配置导入"""
        try:
            from reddit_dump_config import (
                get_ticker_mapping, get_finance_subreddits,
                get_financial_keywords, validate_config
            )
            
            # 验证配置
            ticker_mapping = get_ticker_mapping()
            self.assertIsInstance(ticker_mapping, dict)
            self.assertGreater(len(ticker_mapping), 0)
            
            subreddits = get_finance_subreddits()
            self.assertIsInstance(subreddits, set)
            self.assertGreater(len(subreddits), 0)
            
            keywords = get_financial_keywords()
            self.assertIsInstance(keywords, set)
            self.assertGreater(len(keywords), 0)
            
            # 验证配置有效性
            errors = validate_config()
            self.assertEqual(len(errors), 0, f"配置验证失败: {errors}")
            
        except ImportError as e:
            self.fail(f"无法导入配置: {e}")
    
    def test_processor_initialization(self):
        """测试处理器初始化"""
        try:
            from reddit_dump_processor import RedditDumpProcessor
            
            processor = RedditDumpProcessor(
                dump_dir=str(self.dump_dir),
                output_dir=str(self.output_dir),
                start_date='2025-01-01',
                end_date='2025-06-01'
            )
            
            self.assertEqual(processor.dump_dir, self.dump_dir)
            self.assertEqual(processor.output_dir, self.output_dir)
            self.assertEqual(processor.start_date, datetime(2025, 1, 1))
            self.assertEqual(processor.end_date, datetime(2025, 6, 1))
            
        except ImportError as e:
            self.fail(f"无法导入处理器: {e}")
    
    def test_post_processing(self):
        """测试帖子处理功能"""
        try:
            from reddit_dump_processor import RedditDumpProcessor
            
            processor = RedditDumpProcessor(
                dump_dir=str(self.dump_dir),
                output_dir=str(self.output_dir),
                start_date='2025-01-01',
                end_date='2025-06-01'
            )
            
            # 测试有效帖子
            valid_post = {
                'created_utc': int(datetime(2025, 3, 15).timestamp()),
                'subreddit': 'stocks',
                'title': 'AAPL earnings discussion',
                'selftext': 'Apple just released their quarterly earnings. What do you think?',
                'id': 'test123',
                'author': 'testuser',
                'ups': 100,
                'num_comments': 25,
                'url': 'https://reddit.com/r/stocks/comments/test123'
            }
            
            result = processor.process_post(valid_post)
            self.assertIsNotNone(result)
            self.assertIsInstance(result, list)
            self.assertGreater(len(result), 0)
            
            # 检查结果格式
            ticker, post_record = result[0]
            self.assertEqual(ticker, 'AAPL')
            self.assertIn('platform', post_record)
            self.assertIn('post_id', post_record)
            self.assertIn('title', post_record)
            self.assertIn('sentiment', post_record)
            self.assertIn('engagement_score', post_record)
            
            # 测试无效帖子（日期超出范围）
            invalid_post = {
                'created_utc': int(datetime(2024, 1, 1).timestamp()),
                'subreddit': 'stocks',
                'title': 'Some random post',
                'selftext': 'This is not about stocks',
                'id': 'test456'
            }
            
            result = processor.process_post(invalid_post)
            self.assertIsNone(result)
            
        except ImportError as e:
            self.fail(f"无法导入处理器: {e}")
    
    def test_sentiment_analysis(self):
        """测试情感分析"""
        try:
            from reddit_dump_processor import RedditDumpProcessor
            
            processor = RedditDumpProcessor(
                dump_dir=str(self.dump_dir),
                output_dir=str(self.output_dir),
                start_date='2025-01-01',
                end_date='2025-06-01'
            )
            
            # 测试看涨情感
            bullish_sentiment = processor.determine_sentiment(
                "AAPL to the moon!", 
                "This stock is going up! Buy buy buy!"
            )
            self.assertEqual(bullish_sentiment, 'bullish')
            
            # 测试看跌情感
            bearish_sentiment = processor.determine_sentiment(
                "AAPL crash incoming", 
                "This stock is going down! Sell everything!"
            )
            self.assertEqual(bearish_sentiment, 'bearish')
            
            # 测试中性情感
            neutral_sentiment = processor.determine_sentiment(
                "AAPL analysis", 
                "This is a detailed analysis of the company"
            )
            self.assertEqual(neutral_sentiment, 'neutral')
            
        except ImportError as e:
            self.fail(f"无法导入处理器: {e}")
    
    def test_ticker_extraction(self):
        """测试股票代码提取"""
        try:
            from reddit_dump_processor import RedditDumpProcessor
            
            processor = RedditDumpProcessor(
                dump_dir=str(self.dump_dir),
                output_dir=str(self.output_dir),
                start_date='2025-01-01',
                end_date='2025-06-01'
            )
            
            # 测试明确的股票代码
            tickers = processor.extract_tickers_from_text("AAPL earnings are great")
            self.assertIn('AAPL', tickers)
            
            # 测试公司名称
            tickers = processor.extract_tickers_from_text("Apple just released new iPhone")
            self.assertIn('AAPL', tickers)
            
            # 测试多个股票代码
            tickers = processor.extract_tickers_from_text("AAPL vs MSFT comparison")
            self.assertIn('AAPL', tickers)
            self.assertIn('MSFT', tickers)
            
            # 测试无相关内容
            tickers = processor.extract_tickers_from_text("Random text about weather")
            self.assertEqual(len(tickers), 0)
            
        except ImportError as e:
            self.fail(f"无法导入处理器: {e}")
    
    def test_file_processing(self):
        """测试文件处理"""
        try:
            from reddit_dump_processor import RedditDumpProcessor
            
            processor = RedditDumpProcessor(
                dump_dir=str(self.dump_dir),
                output_dir=str(self.output_dir),
                start_date='2025-01-01',
                end_date='2025-06-01'
            )
            
            # 创建测试数据
            test_posts = [
                {
                    'created_utc': int(datetime(2025, 3, 15).timestamp()),
                    'subreddit': 'stocks',
                    'title': 'AAPL earnings discussion',
                    'selftext': 'Apple earnings are amazing!',
                    'id': 'test1',
                    'author': 'user1',
                    'ups': 100,
                    'num_comments': 25
                },
                {
                    'created_utc': int(datetime(2025, 3, 16).timestamp()),
                    'subreddit': 'investing',
                    'title': 'NVDA AI boom',
                    'selftext': 'NVIDIA is crushing it with AI chips!',
                    'id': 'test2',
                    'author': 'user2',
                    'ups': 200,
                    'num_comments': 50
                }
            ]
            
            # 创建测试文件
            test_file = self.create_test_dump_file('test.jsonl', test_posts)
            
            # 处理文件
            result = processor.process_dump_file(test_file)
            
            # 验证结果
            self.assertIsInstance(result, dict)
            self.assertIn('AAPL', result)
            self.assertIn('NVDA', result)
            
            # 验证帖子数量
            self.assertEqual(len(result['AAPL']), 1)
            self.assertEqual(len(result['NVDA']), 1)
            
        except ImportError as e:
            self.fail(f"无法导入处理器: {e}")
    
    def test_output_format(self):
        """测试输出格式"""
        try:
            from reddit_dump_processor import RedditDumpProcessor
            
            processor = RedditDumpProcessor(
                dump_dir=str(self.dump_dir),
                output_dir=str(self.output_dir),
                start_date='2025-01-01',
                end_date='2025-06-01'
            )
            
            # 创建测试数据
            test_posts = {
                'AAPL': [
                    {
                        "platform": "reddit",
                        "post_id": "reddit_2025-03-15_test1",
                        "title": "AAPL earnings discussion",
                        "content": "Apple earnings are amazing!",
                        "author": "user1",
                        "created_time": "2025-03-15T10:00:00",
                        "url": "https://reddit.com/r/stocks/comments/test1",
                        "upvotes": 100,
                        "comments_count": 25,
                        "ticker": "AAPL",
                        "sentiment": "bullish",
                        "engagement_score": 137.5,
                        "source_subreddit": "stocks",
                        "hashtags": None
                    }
                ]
            }
            
            # 保存数据
            processor.save_posts_by_date(test_posts)
            
            # 验证输出文件
            output_file = self.output_dir / "AAPL_social_media" / "reddit_2025-03-15.json"
            self.assertTrue(output_file.exists())
            
            # 验证文件内容
            with open(output_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.assertIsInstance(data, list)
            self.assertEqual(len(data), 1)
            
            post = data[0]
            self.assertEqual(post['platform'], 'reddit')
            self.assertEqual(post['ticker'], 'AAPL')
            self.assertEqual(post['sentiment'], 'bullish')
            
        except ImportError as e:
            self.fail(f"无法导入处理器: {e}")

def run_basic_tests():
    """运行基本测试"""
    print("运行Reddit转储处理器基本测试...")
    
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加测试
    suite.addTest(TestRedditDumpProcessor('test_config_import'))
    suite.addTest(TestRedditDumpProcessor('test_processor_initialization'))
    suite.addTest(TestRedditDumpProcessor('test_sentiment_analysis'))
    suite.addTest(TestRedditDumpProcessor('test_ticker_extraction'))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

def run_integration_tests():
    """运行集成测试"""
    print("运行Reddit转储处理器集成测试...")
    
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加测试
    suite.addTest(TestRedditDumpProcessor('test_post_processing'))
    suite.addTest(TestRedditDumpProcessor('test_file_processing'))
    suite.addTest(TestRedditDumpProcessor('test_output_format'))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()

def main():
    """主测试函数"""
    print("=" * 60)
    print("Reddit转储处理器测试")
    print("=" * 60)
    
    # 运行基本测试
    basic_success = run_basic_tests()
    
    print("\n" + "=" * 60)
    
    # 运行集成测试
    integration_success = run_integration_tests()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if basic_success and integration_success:
        print("✓ 所有测试通过！")
        print("Reddit转储处理器已准备就绪。")
    else:
        print("✗ 部分测试失败")
        if not basic_success:
            print("  - 基本功能测试失败")
        if not integration_success:
            print("  - 集成测试失败")
    
    print("\n要开始处理Reddit转储文件，请运行:")
    print("python reddit_dump_processor.py --dump-dir /path/to/your/dumps")

if __name__ == '__main__':
    main()
