#!/usr/bin/env python3
"""
在Poetry环境中运行Reddit测试
"""

import subprocess
import sys
import os
from pathlib import Path

def run_with_poetry():
    """使用Poetry运行测试"""
    print("🔍 检测Poetry环境...")
    
    # 检查是否有pyproject.toml
    if Path("pyproject.toml").exists():
        print("✓ 检测到Poetry项目")
        
        # 使用poetry run执行
        try:
            print("📦 使用Poetry运行Reddit连接测试...")
            result = subprocess.run([
                "poetry", "run", "python", "test_reddit_connection.py"
            ], capture_output=True, text=True, timeout=30)
            
            print("输出:")
            print(result.stdout)
            if result.stderr:
                print("错误:")
                print(result.stderr)
            
            return result.returncode == 0
            
        except subprocess.TimeoutExpired:
            print("❌ 测试超时")
            return False
        except FileNotFoundError:
            print("❌ Poetry未找到，尝试直接运行...")
            return run_direct()
        except Exception as e:
            print(f"❌ Poetry运行失败: {e}")
            return run_direct()
    else:
        print("⚠️  未检测到Poetry项目，直接运行...")
        return run_direct()

def run_direct():
    """直接运行测试"""
    try:
        print("🐍 直接运行Python测试...")
        result = subprocess.run([
            sys.executable, "test_reddit_connection.py"
        ], capture_output=True, text=True, timeout=30)
        
        print("输出:")
        print(result.stdout)
        if result.stderr:
            print("错误:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 直接运行失败: {e}")
        return False

def check_env_config():
    """检查环境配置"""
    print("\n🔧 检查.env配置...")
    
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env文件不存在")
        return False
    
    with open(env_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    issues = []
    
    if "REDDIT_USERNAME=your_reddit_username" in content:
        issues.append("Reddit用户名未设置")
    
    if "REDDIT_PASSWORD=your_reddit_password" in content:
        issues.append("Reddit密码未设置")
    
    if issues:
        print("⚠️  配置问题:")
        for issue in issues:
            print(f"   - {issue}")
        
        print("\n💡 解决方案:")
        print("1. 手动编辑.env文件，设置正确的Reddit用户名和密码")
        print("2. 或者运行: python reddit_api_troubleshoot.py")
        return False
    
    print("✓ 环境配置检查通过")
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("Reddit API测试运行器")
    print("=" * 60)
    
    # 检查环境配置
    if not check_env_config():
        return 1
    
    # 运行测试
    if run_with_poetry():
        print("\n✅ Reddit API测试成功！")
        print("现在可以开始收集Reddit数据了")
        return 0
    else:
        print("\n❌ Reddit API测试失败")
        print("请检查API配置或运行故障排除工具")
        return 1

if __name__ == '__main__':
    exit(main())
