#!/usr/bin/env python3
"""
Reddit转储处理器示例脚本

该脚本演示如何使用Reddit转储处理器来处理历史Reddit数据，
并生成与AI对冲基金回测系统兼容的社交媒体数据。
"""

import os
import json
import tempfile
import gzip
from datetime import datetime, timedelta
from pathlib import Path

def create_sample_reddit_dump(dump_dir: Path, num_posts: int = 1000):
    """
    创建示例Reddit转储文件用于测试
    
    Args:
        dump_dir: 转储文件目录
        num_posts: 要生成的帖子数量
    """
    print(f"创建示例Reddit转储文件到 {dump_dir}")
    
    # 确保目录存在
    dump_dir.mkdir(parents=True, exist_ok=True)
    
    # 示例子版块
    subreddits = ['stocks', 'investing', 'wallstreetbets', 'SecurityAnalysis', 'technology']
    
    # 示例帖子模板
    post_templates = [
        {
            "title": "AAPL earnings discussion - what are your thoughts?",
            "content": "Apple just released their quarterly earnings. Revenue beat expectations but iPhone sales were down. What do you think about the stock price movement?",
            "ticker_hints": ["AAPL", "Apple", "iPhone"]
        },
        {
            "title": "NVDA AI chip demand continues to surge",
            "content": "NVIDIA's data center revenue is through the roof. The AI boom is real and NVDA is the biggest beneficiary. Are we in a bubble?",
            "ticker_hints": ["NVDA", "NVIDIA", "AI chip"]
        },
        {
            "title": "Microsoft Azure growth slowing down?",
            "content": "MSFT reported slower Azure growth this quarter. Competition from AWS and Google Cloud is heating up. Time to sell?",
            "ticker_hints": ["MSFT", "Microsoft", "Azure"]
        },
        {
            "title": "Tesla production numbers disappointing",
            "content": "TSLA missed production targets again. Elon's promises vs reality. When will Tesla actually deliver on their promises?",
            "ticker_hints": ["TSLA", "Tesla", "Elon"]
        },
        {
            "title": "Meta's metaverse bet looking worse every quarter",
            "content": "META continues to burn billions on VR/AR with little to show for it. Facebook and Instagram are still cash cows but for how long?",
            "ticker_hints": ["META", "Meta", "Facebook"]
        }
    ]
    
    # 生成帖子数据
    posts = []
    start_date = datetime(2025, 1, 1)
    
    for i in range(num_posts):
        # 随机选择模板和子版块
        template = post_templates[i % len(post_templates)]
        subreddit = subreddits[i % len(subreddits)]
        
        # 生成时间戳（在指定日期范围内）
        days_offset = i % 150  # 分布在150天内
        post_date = start_date + timedelta(days=days_offset)
        created_utc = int(post_date.timestamp())
        
        # 创建帖子数据
        post = {
            "created_utc": created_utc,
            "subreddit": subreddit,
            "title": template["title"],
            "selftext": template["content"],
            "id": f"post_{i:06d}",
            "author": f"user_{i % 100}",
            "ups": 50 + (i % 500),  # 50-549点赞
            "num_comments": 5 + (i % 100),  # 5-104评论
            "url": f"https://reddit.com/r/{subreddit}/comments/post_{i:06d}",
            "score": 50 + (i % 500)
        }
        
        posts.append(post)
    
    # 保存为JSONL文件
    dump_file = dump_dir / "reddit_sample_2025.jsonl"
    with open(dump_file, 'w', encoding='utf-8') as f:
        for post in posts:
            f.write(json.dumps(post) + '\n')
    
    print(f"创建了 {len(posts)} 个示例帖子到 {dump_file}")
    
    # 创建压缩版本
    compressed_file = dump_dir / "reddit_sample_2025_compressed.jsonl.gz"
    with gzip.open(compressed_file, 'wt', encoding='utf-8') as f:
        for post in posts:
            f.write(json.dumps(post) + '\n')
    
    print(f"创建了压缩版本 {compressed_file}")
    
    return [dump_file, compressed_file]

def run_processing_example():
    """
    运行Reddit转储处理示例
    """
    print("=" * 60)
    print("Reddit转储处理器示例")
    print("=" * 60)
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        dump_dir = temp_path / "reddit_dumps"
        output_dir = temp_path / "social_media_data"
        
        # 步骤1: 创建示例转储文件
        print("\n步骤1: 创建示例Reddit转储文件")
        dump_files = create_sample_reddit_dump(dump_dir, num_posts=500)
        
        # 步骤2: 运行处理器
        print("\n步骤2: 运行Reddit转储处理器")
        
        # 导入处理器
        try:
            from reddit_dump_processor import RedditDumpProcessor
            
            # 创建处理器实例
            processor = RedditDumpProcessor(
                dump_dir=str(dump_dir),
                output_dir=str(output_dir),
                start_date='2025-01-01',
                end_date='2025-06-01'
            )
            
            # 运行处理
            processor.process_all_dumps()
            
            # 步骤3: 检查输出
            print("\n步骤3: 检查处理结果")
            
            if output_dir.exists():
                print(f"\n输出目录: {output_dir}")
                
                # 列出生成的文件
                for ticker_dir in output_dir.iterdir():
                    if ticker_dir.is_dir():
                        print(f"\n{ticker_dir.name}:")
                        for json_file in ticker_dir.glob("*.json"):
                            # 读取并显示文件信息
                            try:
                                with open(json_file, 'r', encoding='utf-8') as f:
                                    data = json.load(f)
                                print(f"  {json_file.name}: {len(data)} 个帖子")
                                
                                # 显示第一个帖子的示例
                                if data:
                                    first_post = data[0]
                                    print(f"    示例: {first_post['title'][:50]}...")
                                    print(f"    情感: {first_post['sentiment']}")
                                    print(f"    参与度: {first_post['engagement_score']}")
                                    
                            except Exception as e:
                                print(f"  {json_file.name}: 读取错误 - {e}")
            else:
                print("没有生成输出文件")
                
        except ImportError as e:
            print(f"无法导入处理器: {e}")
            print("请确保 reddit_dump_processor.py 在当前目录中")
        except Exception as e:
            print(f"处理过程中出错: {e}")
    
    print("\n" + "=" * 60)
    print("示例完成")
    print("=" * 60)

def demonstrate_config_customization():
    """
    演示如何自定义配置
    """
    print("\n" + "=" * 60)
    print("配置自定义示例")
    print("=" * 60)
    
    try:
        from reddit_dump_config import (
            get_ticker_mapping, get_finance_subreddits, 
            get_financial_keywords, validate_config
        )
        
        print("\n当前配置:")
        print(f"支持的股票代码: {list(get_ticker_mapping().keys())}")
        print(f"监控的子版块数量: {len(get_finance_subreddits())}")
        print(f"金融关键词数量: {len(get_financial_keywords())}")
        
        # 验证配置
        errors = validate_config()
        if errors:
            print(f"\n配置验证失败: {errors}")
        else:
            print("\n配置验证通过 ✓")
            
        # 显示一些配置示例
        print("\n示例股票代码映射:")
        ticker_mapping = get_ticker_mapping()
        for ticker, keywords in list(ticker_mapping.items())[:3]:
            print(f"  {ticker}: {keywords[:5]}...")
        
        print("\n示例子版块:")
        subreddits = get_finance_subreddits()
        print(f"  {list(subreddits)[:10]}...")
        
        print("\n示例金融关键词:")
        keywords = get_financial_keywords()
        print(f"  {list(keywords)[:10]}...")
        
    except ImportError as e:
        print(f"无法导入配置: {e}")

def show_integration_example():
    """
    显示与AI对冲基金系统集成的示例
    """
    print("\n" + "=" * 60)
    print("AI对冲基金系统集成示例")
    print("=" * 60)
    
    print("\n1. 处理Reddit转储数据:")
    print("python reddit_dump_processor.py \\")
    print("  --dump-dir /path/to/reddit/dumps \\")
    print("  --output-dir social_media_data \\")
    print("  --start-date 2025-01-01 \\")
    print("  --end-date 2025-06-01 \\")
    print("  --tickers AAPL MSFT NVDA")
    
    print("\n2. 运行AI对冲基金回测:")
    print("python src/backtester.py \\")
    print("  --tickers AAPL MSFT NVDA \\")
    print("  --start-date 2025-01-01 \\")
    print("  --end-date 2025-06-01 \\")
    print("  --use_local_social_media")
    
    print("\n3. 生成的数据结构:")
    print("social_media_data/")
    print("├── AAPL_social_media/")
    print("│   ├── reddit_2025-01-01.json")
    print("│   ├── reddit_2025-01-02.json")
    print("│   └── ...")
    print("├── MSFT_social_media/")
    print("└── NVDA_social_media/")
    
    print("\n4. 数据格式兼容性:")
    print("生成的JSON文件与现有社交媒体分析系统完全兼容，")
    print("包含所有必需的字段：platform, post_id, title, content,")
    print("sentiment, engagement_score, ticker等。")

def main():
    """
    主函数 - 运行所有示例
    """
    print("Reddit转储处理器完整示例")
    
    # 运行处理示例
    run_processing_example()
    
    # 演示配置自定义
    demonstrate_config_customization()
    
    # 显示集成示例
    show_integration_example()
    
    print("\n" + "=" * 60)
    print("所有示例完成！")
    print("=" * 60)
    print("\n要开始处理真实的Reddit转储文件，请运行:")
    print("python reddit_dump_processor.py --dump-dir /path/to/your/reddit/dumps")

if __name__ == '__main__':
    main()
