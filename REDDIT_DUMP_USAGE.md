# Reddit历史转储文件处理器使用说明

## 概述

Reddit历史转储文件处理器是一个专门用于处理Reddit历史数据转储文件的工具，它可以从大型Reddit数据集中提取与特定股票代码相关的帖子和评论，并将其转换为与AI对冲基金回测系统兼容的JSON格式。

## 功能特性

- **处理多种格式**: 支持JSONL、JSON格式，以及它们的压缩版本（.gz, .bz2, .xz）
- **智能过滤**: 按日期范围、股票代码、子版块和金融关键词过滤内容
- **高效处理**: 流式处理大文件，避免内存溢出
- **结构化输出**: 生成与现有社交媒体分析系统兼容的JSON格式
- **情感分析**: 基于关键词的简单情感分析
- **可配置**: 通过配置文件自定义股票代码、关键词等

## 安装要求

```bash
pip install python-dateutil
```

## 文件结构

```
reddit_dump_processor.py    # 主处理脚本
reddit_dump_config.py       # 配置文件
REDDIT_DUMP_USAGE.md        # 使用说明（本文件）
```

## 使用方法

### 基本用法

```bash
python reddit_dump_processor.py --dump-dir /path/to/reddit/dumps
```

### 完整参数

```bash
python reddit_dump_processor.py \
  --dump-dir /path/to/reddit/dumps \
  --output-dir social_media_data \
  --start-date 2025-01-01 \
  --end-date 2025-06-01 \
  --tickers AAPL MSFT NVDA
```

### 参数说明

- `--dump-dir`: Reddit转储文件目录（必需）
- `--output-dir`: 输出目录（默认: social_media_data）
- `--start-date`: 开始日期，格式YYYY-MM-DD（默认: 2025-01-01）
- `--end-date`: 结束日期，格式YYYY-MM-DD（默认: 2025-06-01）
- `--tickers`: 要处理的股票代码列表（默认: 所有支持的股票）

## Reddit转储文件格式

处理器期望的Reddit转储文件格式为JSONL（每行一个JSON对象），每个对象应包含以下字段：

```json
{
  "created_utc": 1640995200,
  "subreddit": "stocks",
  "title": "AAPL earnings discussion",
  "selftext": "What do you think about Apple's latest earnings?",
  "id": "abc123",
  "author": "investor123",
  "ups": 250,
  "num_comments": 45,
  "url": "https://reddit.com/r/stocks/comments/abc123"
}
```

### 必需字段

- `created_utc`: Unix时间戳
- `subreddit`: 子版块名称
- `title`: 帖子标题

### 可选字段

- `selftext` 或 `body`: 帖子内容
- `id`: 帖子ID
- `author`: 作者
- `ups` 或 `score`: 点赞数
- `num_comments`: 评论数
- `url`: 帖子URL

## 输出格式

处理器生成的输出文件遵循现有社交媒体分析系统的格式：

```
social_media_data/
├── AAPL_social_media/
│   ├── reddit_2025-01-01.json
│   ├── reddit_2025-01-02.json
│   └── ...
├── MSFT_social_media/
│   ├── reddit_2025-01-01.json
│   └── ...
└── NVDA_social_media/
    ├── reddit_2025-01-01.json
    └── ...
```

每个JSON文件包含该日期的所有相关帖子：

```json
[
  {
    "platform": "reddit",
    "post_id": "reddit_2025-01-01_abc123",
    "title": "AAPL earnings discussion",
    "content": "What do you think about Apple's latest earnings?",
    "author": "investor123",
    "created_time": "2025-01-01T09:30:00",
    "url": "https://reddit.com/r/stocks/comments/abc123",
    "upvotes": 250,
    "comments_count": 45,
    "ticker": "AAPL",
    "sentiment": "bullish",
    "engagement_score": 317.5,
    "source_subreddit": "stocks",
    "hashtags": null
  }
]
```

## 配置自定义

### 修改股票代码

编辑 `reddit_dump_config.py` 中的 `TICKER_TO_COMPANY` 字典：

```python
TICKER_TO_COMPANY = {
    'AAPL': ['Apple', 'AAPL', 'Apple Inc', 'iPhone', 'iPad'],
    'CUSTOM': ['Custom Company', 'CUSTOM', 'Custom Inc'],
    # 添加更多股票代码...
}
```

### 修改监控子版块

编辑 `FINANCE_SUBREDDITS` 集合：

```python
FINANCE_SUBREDDITS = {
    'investing', 'stocks', 'wallstreetbets',
    'your_custom_subreddit',
    # 添加更多子版块...
}
```

### 修改金融关键词

编辑 `FINANCIAL_KEYWORDS` 集合：

```python
FINANCIAL_KEYWORDS = {
    'earnings', 'revenue', 'profit',
    'your_custom_keyword',
    # 添加更多关键词...
}
```

## 性能优化

### 大文件处理

- 处理器使用流式读取，可以处理GB级别的文件
- 每处理10,000行显示一次进度
- 支持压缩文件直接处理

### 内存使用

- 按日期分批保存，避免内存积累
- 自动去重，避免重复数据

### 处理速度

- 典型处理速度：10,000-50,000行/分钟（取决于硬件和文件格式）
- 压缩文件处理速度较慢，但节省存储空间

## 故障排除

### 常见问题

1. **文件格式错误**
   ```
   错误: JSON解析失败
   解决: 确保转储文件为有效的JSONL格式
   ```

2. **内存不足**
   ```
   错误: MemoryError
   解决: 减少batch_size配置或分批处理文件
   ```

3. **没有找到相关内容**
   ```
   错误: 处理完成但没有输出文件
   解决: 检查日期范围、股票代码配置和转储文件内容
   ```

### 日志分析

处理器会生成详细的日志文件 `reddit_dump_processor.log`：

```
2025-01-01 10:00:00 - INFO - 初始化Reddit转储处理器
2025-01-01 10:00:01 - INFO - 找到 5 个转储文件
2025-01-01 10:00:02 - INFO - 处理文件: /path/to/dump1.jsonl
2025-01-01 10:01:00 - INFO - 已处理 10000 行，找到 150 个相关帖子
```

## 与AI对冲基金系统集成

处理器生成的数据可以直接用于AI对冲基金回测系统：

```bash
# 处理Reddit数据
python reddit_dump_processor.py --dump-dir /path/to/dumps

# 运行回测（使用本地社交媒体数据）
python src/backtester.py \
  --tickers AAPL MSFT NVDA \
  --start-date 2025-01-01 \
  --end-date 2025-06-01 \
  --use_local_social_media
```

## 数据质量

### 过滤标准

- 只处理金融相关子版块的帖子
- 内容必须包含股票代码或相关关键词
- 支持日期范围过滤
- 自动去重重复帖子

### 情感分析

- 基于关键词的简单情感分析
- 支持bullish（看涨）、bearish（看跌）、neutral（中性）
- 可通过配置文件自定义情感关键词

### 参与度计算

```
engagement_score = upvotes + (comments_count * 1.5)
```

## 扩展功能

### 添加新的数据源

可以扩展处理器以支持其他社交媒体平台的转储文件，只需：

1. 修改 `process_post` 方法以处理不同的字段格式
2. 更新配置文件以包含新平台的特定设置
3. 调整输出格式以标识数据来源

### 高级情感分析

可以集成更复杂的情感分析模型：

1. 使用预训练的NLP模型
2. 集成金融特定的情感词典
3. 考虑上下文和讽刺检测

## 许可证

本工具作为AI对冲基金回测系统的一部分，遵循项目的整体许可证。
