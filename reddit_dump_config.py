#!/usr/bin/env python3
"""
Reddit转储处理器配置文件

该文件包含Reddit转储处理器的配置选项，包括股票代码映射、
子版块列表、关键词等。用户可以根据需要修改这些配置。
"""

# 股票代码到公司名称和相关关键词的映射
# 可以根据需要添加更多股票代码和关键词
TICKER_TO_COMPANY = {
    'AAPL': [
        'Apple', 'AAPL', 'Apple Inc', 'iPhone', 'iPad', 'Mac', 'iOS', 
        'MacBook', 'iMac', 'Apple Watch', 'AirPods', 'Tim Cook', 'App Store',
        'iTunes', 'iCloud', 'Safari', 'Siri', 'FaceTime'
    ],
    'MSFT': [
        'Microsoft', 'MSFT', 'Microsoft Corp', 'Windows', 'Azure', 'Office', 
        'Xbox', 'Satya Nadella', 'Teams', 'OneDrive', 'Outlook', 'PowerPoint',
        'Excel', 'Word', 'Surface', 'Bing', 'LinkedIn', 'GitHub'
    ],
    'NVDA': [
        'NVIDIA', 'NVDA', 'Nvidia Corp', 'GeForce', 'RTX', 'GPU', 'AI chip', 
        'CUDA', 'Jensen Huang', 'graphics card', 'gaming', 'data center',
        'autonomous driving', 'machine learning', 'deep learning', 'Tegra'
    ],
    'GOOGL': [
        'Google', 'Alphabet', 'GOOGL', 'GOOG', 'YouTube', 'Android', 'Chrome',
        'Gmail', 'Google Search', 'Google Cloud', 'Pixel', 'Sundar Pichai',
        'AdWords', 'Google Maps', 'Google Drive', 'Waymo', 'DeepMind'
    ],
    'AMZN': [
        'Amazon', 'AMZN', 'AWS', 'Prime', 'Alexa', 'Bezos', 'Jeff Bezos',
        'Amazon Web Services', 'Kindle', 'Fire TV', 'Echo', 'Whole Foods',
        'Amazon Prime', 'marketplace', 'e-commerce', 'cloud computing'
    ],
    'TSLA': [
        'Tesla', 'TSLA', 'Elon Musk', 'Model S', 'Model 3', 'Model Y', 
        'Cybertruck', 'electric vehicle', 'EV', 'Supercharger', 'Autopilot',
        'Full Self Driving', 'FSD', 'Gigafactory', 'battery', 'solar'
    ],
    'META': [
        'Meta', 'Facebook', 'META', 'FB', 'Instagram', 'WhatsApp', 'Metaverse',
        'Mark Zuckerberg', 'Oculus', 'VR', 'virtual reality', 'Threads',
        'Messenger', 'Reality Labs', 'Quest'
    ],
    'NFLX': [
        'Netflix', 'NFLX', 'streaming', 'Reed Hastings', 'original content',
        'binge watching', 'subscription', 'video streaming'
    ],
    'AMD': [
        'AMD', 'Advanced Micro Devices', 'Ryzen', 'EPYC', 'Radeon', 'CPU',
        'processor', 'Lisa Su', 'Zen', 'RDNA'
    ],
    'INTC': [
        'Intel', 'INTC', 'processor', 'CPU', 'chip', 'semiconductor',
        'Core i7', 'Core i5', 'Xeon', 'Pat Gelsinger'
    ],
    'CRM': [
        'Salesforce', 'CRM', 'Marc Benioff', 'cloud software', 'SaaS',
        'customer relationship management'
    ],
    'ORCL': [
        'Oracle', 'ORCL', 'database', 'Larry Ellison', 'cloud infrastructure',
        'enterprise software'
    ],
    'ADBE': [
        'Adobe', 'ADBE', 'Photoshop', 'Creative Cloud', 'PDF', 'Acrobat',
        'Illustrator', 'Premiere', 'creative software'
    ],
    'PYPL': [
        'PayPal', 'PYPL', 'digital payments', 'Venmo', 'online payments',
        'fintech', 'Dan Schulman'
    ],
    'SHOP': [
        'Shopify', 'SHOP', 'e-commerce platform', 'online store', 'Tobi Lutke'
    ],
    'SQ': [
        'Square', 'Block', 'SQ', 'Jack Dorsey', 'payment processing',
        'Cash App', 'point of sale'
    ],
    'ZOOM': [
        'Zoom', 'ZM', 'video conferencing', 'Eric Yuan', 'remote work',
        'video calls', 'webinar'
    ],
    'UBER': [
        'Uber', 'rideshare', 'ride-hailing', 'Uber Eats', 'gig economy',
        'transportation'
    ],
    'LYFT': [
        'Lyft', 'rideshare', 'ride-hailing', 'transportation', 'gig economy'
    ],
    'SPOT': [
        'Spotify', 'SPOT', 'music streaming', 'Daniel Ek', 'podcast',
        'audio streaming'
    ],
    'TWTR': [
        'Twitter', 'TWTR', 'social media', 'tweets', 'microblogging'
    ],
    'SNAP': [
        'Snapchat', 'SNAP', 'Evan Spiegel', 'social media', 'AR filters',
        'disappearing messages'
    ],
    'PINS': [
        'Pinterest', 'PINS', 'Ben Silbermann', 'visual discovery',
        'social media', 'pinning'
    ]
}

# 相关金融子版块列表
# 可以根据需要添加更多子版块
FINANCE_SUBREDDITS = {
    # 主要投资和股票相关
    'investing', 'stocks', 'SecurityAnalysis', 'ValueInvesting', 
    'financialindependence', 'StockMarket', 'wallstreetbets', 
    'options', 'dividends', 'pennystocks', 'investing_discussion',
    
    # 金融和经济
    'finance', 'economy', 'business', 'Economics', 'personalfinance',
    'financialplanning', 'retirement', 'Fire', 'leanfire', 'fatfire',
    
    # 科技和商业新闻
    'technology', 'tech', 'artificial', 'MachineLearning', 'singularity',
    'Futurology', 'business_news', 'entrepreneur', 'startups',
    
    # 新闻相关
    'news', 'worldnews', 'business', 'technews', 'gadgets',
    
    # 加密货币（如果相关）
    'cryptocurrency', 'Bitcoin', 'ethereum', 'CryptoCurrency',
    
    # 特定公司相关
    'apple', 'microsoft', 'nvidia', 'google', 'amazon', 'tesla',
    'facebook', 'netflix', 'AMD', 'intel',
    
    # 其他相关
    'dataisbeautiful', 'analytics', 'MachineLearning', 'artificial',
    'programming', 'cscareerquestions', 'entrepreneur'
}

# 金融关键词列表
# 用于识别金融相关内容
FINANCIAL_KEYWORDS = {
    # 基本金融术语
    'earnings', 'revenue', 'profit', 'loss', 'stock', 'share', 'dividend',
    'market cap', 'valuation', 'IPO', 'merger', 'acquisition', 'buyback',
    'quarterly', 'annual', 'guidance', 'forecast', 'analyst', 'rating',
    'upgrade', 'downgrade', 'bull', 'bear', 'rally', 'crash', 'volatility',
    
    # 交易相关
    'buy', 'sell', 'hold', 'long', 'short', 'call', 'put', 'option',
    'strike', 'expiry', 'premium', 'portfolio', 'position', 'trade',
    
    # 财务指标
    'P/E', 'price to earnings', 'EPS', 'earnings per share', 'ROE', 'ROI',
    'debt to equity', 'free cash flow', 'EBITDA', 'gross margin',
    'operating margin', 'net margin',
    
    # 市场术语
    'market', 'nasdaq', 'nyse', 'dow jones', 'S&P 500', 'index',
    'sector', 'industry', 'growth', 'value', 'momentum', 'trend',
    
    # 投资策略
    'investment', 'investing', 'investor', 'fund', 'ETF', 'mutual fund',
    'hedge fund', 'pension fund', 'institutional', 'retail',
    
    # 经济指标
    'GDP', 'inflation', 'interest rate', 'federal reserve', 'fed',
    'unemployment', 'consumer confidence', 'retail sales'
}

# 情感分析关键词
SENTIMENT_KEYWORDS = {
    'positive': [
        'bullish', 'buy', 'long', 'up', 'rise', 'gain', 'profit', 'good', 
        'great', 'excellent', 'amazing', 'fantastic', 'awesome', 'love',
        'strong', 'solid', 'growth', 'boom', 'surge', 'rally', 'moon',
        'rocket', 'diamond hands', 'hodl', 'to the moon'
    ],
    'negative': [
        'bearish', 'sell', 'short', 'down', 'fall', 'loss', 'bad', 
        'terrible', 'awful', 'horrible', 'crash', 'dump', 'tank',
        'plummet', 'disaster', 'bubble', 'overvalued', 'risky',
        'paper hands', 'panic sell', 'rug pull'
    ],
    'neutral': [
        'hold', 'wait', 'watch', 'monitor', 'sideways', 'flat',
        'consolidation', 'range bound', 'uncertain', 'mixed'
    ]
}

# 处理配置
PROCESSING_CONFIG = {
    # 文件处理
    'batch_size': 10000,  # 每批处理的行数
    'max_file_size_mb': 1000,  # 最大文件大小（MB）
    'supported_extensions': ['.jsonl', '.json', '.jsonl.gz', '.json.gz', 
                           '.jsonl.bz2', '.json.bz2', '.jsonl.xz', '.json.xz'],
    
    # 内容过滤
    'min_upvotes': 0,  # 最小点赞数
    'min_comments': 0,  # 最小评论数
    'min_content_length': 10,  # 最小内容长度
    'max_content_length': 10000,  # 最大内容长度
    
    # 参与度计算权重
    'upvote_weight': 1.0,
    'comment_weight': 1.5,
    
    # 日志配置
    'log_level': 'INFO',
    'log_file': 'reddit_dump_processor.log',
    'progress_interval': 10000,  # 每处理多少行显示进度
}

# 输出配置
OUTPUT_CONFIG = {
    'date_format': '%Y-%m-%d',
    'time_format': '%Y-%m-%dT%H:%M:%S',
    'file_naming': '{ticker}_social_media/reddit_{date}.json',
    'indent': 2,
    'ensure_ascii': False,
    'sort_by_time': True,
    'reverse_sort': True,  # 最新的在前
}

# 验证配置
def validate_config():
    """验证配置的有效性"""
    errors = []
    
    # 检查股票代码
    if not TICKER_TO_COMPANY:
        errors.append("TICKER_TO_COMPANY 不能为空")
    
    # 检查子版块
    if not FINANCE_SUBREDDITS:
        errors.append("FINANCE_SUBREDDITS 不能为空")
    
    # 检查关键词
    if not FINANCIAL_KEYWORDS:
        errors.append("FINANCIAL_KEYWORDS 不能为空")
    
    # 检查处理配置
    if PROCESSING_CONFIG['batch_size'] <= 0:
        errors.append("batch_size 必须大于0")
    
    if PROCESSING_CONFIG['max_file_size_mb'] <= 0:
        errors.append("max_file_size_mb 必须大于0")
    
    return errors

# 获取配置函数
def get_ticker_mapping():
    """获取股票代码映射"""
    return TICKER_TO_COMPANY.copy()

def get_finance_subreddits():
    """获取金融子版块集合"""
    return FINANCE_SUBREDDITS.copy()

def get_financial_keywords():
    """获取金融关键词集合"""
    return FINANCIAL_KEYWORDS.copy()

def get_sentiment_keywords():
    """获取情感关键词"""
    return SENTIMENT_KEYWORDS.copy()

def get_processing_config():
    """获取处理配置"""
    return PROCESSING_CONFIG.copy()

def get_output_config():
    """获取输出配置"""
    return OUTPUT_CONFIG.copy()

if __name__ == '__main__':
    # 验证配置
    errors = validate_config()
    if errors:
        print("配置验证失败:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("配置验证通过")
        print(f"支持的股票代码: {list(TICKER_TO_COMPANY.keys())}")
        print(f"监控的子版块数量: {len(FINANCE_SUBREDDITS)}")
        print(f"金融关键词数量: {len(FINANCIAL_KEYWORDS)}")
