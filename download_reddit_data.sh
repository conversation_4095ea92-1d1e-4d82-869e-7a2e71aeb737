#!/bin/bash
# Reddit Data Download Script
# Download Reddit historical dump files for 2025

echo 'Starting Reddit data download...'

# Create download directory
mkdir -p reddit_dumps
cd reddit_dumps

# Download 2025 Reddit submission data (Jan-<PERSON>)
echo 'Downloading submission data...'
echo 'Note: Each file is 2-5GB, total download ~15-30GB'

wget -c https://files.pushshift.io/reddit/submissions/RS_2025-01.zst
wget -c https://files.pushshift.io/reddit/submissions/RS_2025-02.zst
wget -c https://files.pushshift.io/reddit/submissions/RS_2025-03.zst
wget -c https://files.pushshift.io/reddit/submissions/RS_2025-04.zst
wget -c https://files.pushshift.io/reddit/submissions/RS_2025-05.zst
wget -c https://files.pushshift.io/reddit/submissions/RS_2025-06.zst

# Optional: Download comment data (much larger files)
echo 'Comment data download is disabled by default (very large files)'
echo 'Uncomment the lines below if you need comment data:'
# wget -c https://files.pushshift.io/reddit/comments/RC_2025-01.zst
# wget -c https://files.pushshift.io/reddit/comments/RC_2025-02.zst
# wget -c https://files.pushshift.io/reddit/comments/RC_2025-03.zst

echo 'Download completed!'
echo 'Files downloaded:'
ls -lh *.zst

echo ''
echo 'Next steps:'
echo '1. Convert files: python ../convert_reddit_dumps.py --input-dir reddit_dumps --output-dir reddit_dumps_processed'
echo '2. Process data: python ../reddit_dump_processor.py --dump-dir reddit_dumps_processed'
echo '3. Run backtesting: python ../src/backtester.py --tickers AAPL MSFT NVDA --use_local_social_media'

echo ''
echo 'File conversion will be needed before processing:'
echo 'The downloaded .zst files need to be converted to .jsonl format'
echo 'This will require additional disk space (3-5x the compressed size)'
