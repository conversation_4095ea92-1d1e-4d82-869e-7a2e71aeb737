#!/usr/bin/env python3
"""
Reddit转储文件转换工具

将下载的Reddit转储文件转换为处理器支持的格式
支持从.zst格式转换为.jsonl格式
"""

import json
import gzip
import subprocess
import sys
from pathlib import Path
from typing import Optional
import argparse

def check_zstd_available() -> bool:
    """检查zstd工具是否可用"""
    try:
        subprocess.run(['zstd', '--version'], 
                      capture_output=True, check=True)
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def decompress_zst_file(zst_file: Path, output_file: Optional[Path] = None) -> Path:
    """
    解压.zst文件
    
    Args:
        zst_file: 输入的.zst文件路径
        output_file: 输出文件路径，如果为None则自动生成
    
    Returns:
        解压后的文件路径
    """
    if not check_zstd_available():
        raise RuntimeError("zstd工具未安装。请先安装zstd：\n"
                         "Ubuntu/Debian: sudo apt-get install zstd\n"
                         "macOS: brew install zstd\n"
                         "Windows: 下载zstd工具")
    
    if output_file is None:
        output_file = zst_file.with_suffix('')
    
    print(f"解压 {zst_file} -> {output_file}")
    
    try:
        subprocess.run(['zstd', '-d', str(zst_file), '-o', str(output_file)], 
                      check=True)
        print(f"✓ 解压完成: {output_file}")
        return output_file
    except subprocess.CalledProcessError as e:
        raise RuntimeError(f"解压失败: {e}")

def convert_to_jsonl(input_file: Path, output_file: Path, 
                    compress: bool = False, max_lines: Optional[int] = None):
    """
    将Reddit转储文件转换为JSONL格式
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
        compress: 是否压缩输出文件
        max_lines: 最大处理行数（用于测试）
    """
    print(f"转换 {input_file} -> {output_file}")
    
    if compress:
        output_file = output_file.with_suffix(output_file.suffix + '.gz')
        opener = gzip.open
        mode = 'wt'
    else:
        opener = open
        mode = 'w'
    
    processed_lines = 0
    valid_lines = 0
    
    try:
        with open(input_file, 'r', encoding='utf-8') as infile, \
             opener(output_file, mode, encoding='utf-8') as outfile:
            
            for line_num, line in enumerate(infile, 1):
                if max_lines and processed_lines >= max_lines:
                    break
                
                line = line.strip()
                if not line:
                    continue
                
                try:
                    # 验证JSON格式
                    data = json.loads(line)
                    
                    # 检查必需字段
                    required_fields = ['created_utc', 'subreddit', 'id']
                    if all(field in data for field in required_fields):
                        outfile.write(line + '\n')
                        valid_lines += 1
                    
                    processed_lines += 1
                    
                    if processed_lines % 100000 == 0:
                        print(f"已处理 {processed_lines:,} 行，有效行 {valid_lines:,}")
                
                except json.JSONDecodeError:
                    # 跳过无效的JSON行
                    continue
                except Exception as e:
                    print(f"处理第 {line_num} 行时出错: {e}")
                    continue
    
    except Exception as e:
        print(f"转换失败: {e}")
        raise
    
    print(f"✓ 转换完成: {output_file}")
    print(f"  总处理行数: {processed_lines:,}")
    print(f"  有效行数: {valid_lines:,}")
    print(f"  文件大小: {output_file.stat().st_size / 1024 / 1024:.1f} MB")

def download_sample_data(output_dir: Path):
    """
    下载示例Reddit数据用于测试
    """
    print("下载示例Reddit数据...")
    
    # 这里可以添加下载逻辑
    # 由于实际下载需要大量时间，这里提供下载命令
    
    commands = [
        "#!/bin/bash",
        "# Reddit Data Download Script",
        "# Download Reddit historical dump files for 2025",
        "",
        "echo 'Starting Reddit data download...'",
        "",
        "# Create download directory",
        "mkdir -p reddit_dumps",
        "cd reddit_dumps",
        "",
        "# Download 2025 Reddit submission data (Jan-Jun)",
        "echo 'Downloading submission data...'",
        "wget -c https://files.pushshift.io/reddit/submissions/RS_2025-01.zst",
        "wget -c https://files.pushshift.io/reddit/submissions/RS_2025-02.zst",
        "wget -c https://files.pushshift.io/reddit/submissions/RS_2025-03.zst",
        "wget -c https://files.pushshift.io/reddit/submissions/RS_2025-04.zst",
        "wget -c https://files.pushshift.io/reddit/submissions/RS_2025-05.zst",
        "wget -c https://files.pushshift.io/reddit/submissions/RS_2025-06.zst",
        "",
        "# Optional: Download comment data (much larger files)",
        "# echo 'Downloading comment data...'",
        "# wget -c https://files.pushshift.io/reddit/comments/RC_2025-01.zst",
        "# wget -c https://files.pushshift.io/reddit/comments/RC_2025-02.zst",
        "",
        "echo 'Download completed!'",
        "echo 'Files downloaded:'",
        "ls -lh *.zst",
        "",
        "echo 'Next steps:'",
        "echo '1. Convert files: python ../convert_reddit_dumps.py --input-dir reddit_dumps'",
        "echo '2. Process data: python ../reddit_dump_processor.py --dump-dir reddit_dumps_processed'",
    ]

    script_file = output_dir / "download_reddit_data.sh"
    with open(script_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(commands))
    
    print(f"✓ 下载脚本已创建: {script_file}")
    print("请运行该脚本下载Reddit数据")

def main():
    parser = argparse.ArgumentParser(description='Reddit转储文件转换工具')
    parser.add_argument('--input-dir', type=str, default='reddit_dumps',
                       help='输入目录路径')
    parser.add_argument('--output-dir', type=str, default='reddit_dumps_processed',
                       help='输出目录路径')
    parser.add_argument('--compress', action='store_true',
                       help='压缩输出文件')
    parser.add_argument('--max-lines', type=int,
                       help='最大处理行数（用于测试）')
    parser.add_argument('--download-script', action='store_true',
                       help='生成下载脚本')
    
    args = parser.parse_args()
    
    input_dir = Path(args.input_dir)
    output_dir = Path(args.output_dir)
    
    # 创建输出目录
    output_dir.mkdir(parents=True, exist_ok=True)
    
    if args.download_script:
        download_sample_data(output_dir)
        return
    
    if not input_dir.exists():
        print(f"输入目录不存在: {input_dir}")
        print("使用 --download-script 生成下载脚本")
        return
    
    print("=" * 60)
    print("Reddit转储文件转换工具")
    print("=" * 60)
    
    # 查找需要处理的文件
    zst_files = list(input_dir.glob('*.zst'))
    json_files = list(input_dir.glob('*.json'))
    jsonl_files = list(input_dir.glob('*.jsonl'))
    
    if zst_files:
        print(f"\n找到 {len(zst_files)} 个.zst文件需要解压:")
        for zst_file in zst_files:
            print(f"  {zst_file.name}")
        
        # 解压.zst文件
        for zst_file in zst_files:
            try:
                decompressed_file = decompress_zst_file(zst_file)
                
                # 转换为JSONL格式
                output_file = output_dir / f"{decompressed_file.stem}.jsonl"
                convert_to_jsonl(decompressed_file, output_file, 
                               args.compress, args.max_lines)
                
                # 删除临时解压文件（可选）
                # decompressed_file.unlink()
                
            except Exception as e:
                print(f"处理 {zst_file} 失败: {e}")
                continue
    
    elif json_files or jsonl_files:
        all_files = json_files + jsonl_files
        print(f"\n找到 {len(all_files)} 个JSON/JSONL文件需要转换:")
        for file in all_files:
            print(f"  {file.name}")
        
        # 转换现有文件
        for file in all_files:
            try:
                output_file = output_dir / f"{file.stem}.jsonl"
                convert_to_jsonl(file, output_file, 
                               args.compress, args.max_lines)
            except Exception as e:
                print(f"处理 {file} 失败: {e}")
                continue
    
    else:
        print(f"\n在 {input_dir} 中未找到Reddit转储文件")
        print("支持的文件格式: .zst, .json, .jsonl")
        print("\n使用 --download-script 生成下载脚本")
    
    print("\n" + "=" * 60)
    print("转换完成！")
    print("=" * 60)
    
    if output_dir.exists() and list(output_dir.glob('*.jsonl*')):
        print(f"\n处理后的文件位于: {output_dir}")
        print("\n现在可以使用Reddit转储处理器:")
        print(f"python reddit_dump_processor.py --dump-dir {output_dir}")

if __name__ == '__main__':
    main()
