#!/usr/bin/env python3
"""
Reddit实时数据收集系统安装脚本

自动安装依赖包并配置系统
"""

import os
import sys
import subprocess
from pathlib import Path

def print_banner():
    """打印安装横幅"""
    print("=" * 60)
    print("Reddit实时数据收集系统安装程序")
    print("=" * 60)
    print("这个脚本将帮助您安装和配置Reddit数据收集系统")
    print()

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    
    print(f"✓ Python版本: {sys.version.split()[0]}")
    return True

def install_dependencies():
    """安装依赖包"""
    print("\n📦 安装依赖包...")
    
    requirements_file = Path("requirements_reddit.txt")
    
    if not requirements_file.exists():
        print("❌ requirements_reddit.txt文件不存在")
        return False
    
    try:
        # 升级pip
        print("升级pip...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # 安装依赖
        print("安装Reddit系统依赖...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "-r", str(requirements_file)
        ], check=True, capture_output=True, text=True)
        
        print("✓ 依赖包安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        if e.stdout:
            print("标准输出:", e.stdout)
        if e.stderr:
            print("错误输出:", e.stderr)
        return False

def verify_installation():
    """验证安装"""
    print("\n🔍 验证安装...")
    
    required_packages = [
        'praw',
        'python-dotenv', 
        'tqdm',
        'schedule',
        'pandas'
    ]
    
    failed_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package}")
        except ImportError:
            print(f"❌ {package}")
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n⚠️  以下包安装失败: {', '.join(failed_packages)}")
        return False
    
    print("✅ 所有依赖包验证通过")
    return True

def create_directories():
    """创建必要的目录"""
    print("\n📁 创建目录结构...")
    
    directories = [
        "social_media_data",
        "logs",
        "cache"
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(exist_ok=True)
        print(f"✓ {directory}/")
    
    return True

def setup_environment():
    """设置环境变量"""
    print("\n🔧 检查环境配置...")
    
    env_file = Path(".env")
    
    if not env_file.exists():
        print("❌ .env文件不存在")
        return False
    
    # 检查Reddit配置
    with open(env_file, 'r') as f:
        content = f.read()
    
    reddit_vars = [
        'REDDIT_CLIENT_ID',
        'REDDIT_CLIENT_SECRET', 
        'REDDIT_USER_AGENT'
    ]
    
    missing_vars = []
    for var in reddit_vars:
        if f"{var}=" not in content or f"{var}=your_" in content:
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  需要配置Reddit API: {', '.join(missing_vars)}")
        print("请运行: python setup_reddit_api.py")
        return False
    
    print("✓ 环境配置检查通过")
    return True

def run_system_test():
    """运行系统测试"""
    print("\n🧪 运行系统测试...")
    
    try:
        result = subprocess.run([
            sys.executable, "test_reddit_system.py", "--check"
        ], check=True, capture_output=True, text=True)
        
        print("✓ 系统测试通过")
        return True
        
    except subprocess.CalledProcessError as e:
        print("❌ 系统测试失败")
        if e.stdout:
            print(e.stdout)
        if e.stderr:
            print(e.stderr)
        return False

def show_next_steps():
    """显示后续步骤"""
    print("\n🎉 安装完成！")
    print("=" * 60)
    print("后续步骤:")
    print()
    print("1. 配置Reddit API:")
    print("   python setup_reddit_api.py")
    print()
    print("2. 测试数据收集:")
    print("   python reddit_live_collector.py --incremental")
    print()
    print("3. 验证数据质量:")
    print("   python reddit_data_validator.py")
    print()
    print("4. 启动定时收集:")
    print("   python reddit_scheduler.py --mode daily")
    print()
    print("5. 集成到AI系统:")
    print("   python src/backtester.py --use_local_social_media")
    print()
    print("📚 详细文档: REDDIT_LIVE_GUIDE.md")
    print("=" * 60)

def main():
    """主安装流程"""
    print_banner()
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 依赖安装失败，请手动安装:")
        print("pip install -r requirements_reddit.txt")
        return 1
    
    # 验证安装
    if not verify_installation():
        return 1
    
    # 创建目录
    if not create_directories():
        return 1
    
    # 检查环境配置
    env_configured = setup_environment()
    
    # 运行系统测试
    if env_configured:
        run_system_test()
    
    # 显示后续步骤
    show_next_steps()
    
    if not env_configured:
        print("\n⚠️  请先配置Reddit API，然后重新运行测试")
        return 1
    
    return 0

if __name__ == '__main__':
    try:
        exit(main())
    except KeyboardInterrupt:
        print("\n\n安装被用户中断")
        exit(1)
    except Exception as e:
        print(f"\n安装过程中出错: {e}")
        exit(1)
