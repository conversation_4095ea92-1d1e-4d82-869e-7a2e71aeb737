#!/usr/bin/env python3
"""
NVDA价格波动图绘制脚本
分析项目中获取价格数据的逻辑，并绘制NVDA在2025.01.01-2025.06.01期间的价格波动图
"""

import os
import sys
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import numpy as np

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入项目中的价格获取函数
from src.tools.api import get_prices, get_price_data

def analyze_price_data_logic():
    """分析项目中获取价格数据的逻辑"""
    print("=== 项目中获取价格数据的逻辑分析 ===")
    print()
    
    print("1. 数据源：")
    print("   - 主要API：https://api.financialdatasets.ai/prices/")
    print("   - 需要API密钥：FINANCIAL_DATASETS_API_KEY")
    print("   - 支持缓存机制，优先从缓存读取数据")
    print()
    
    print("2. 数据获取流程：")
    print("   a) 首先检查缓存中是否有数据")
    print("   b) 如果缓存中有数据且在指定日期范围内，直接返回")
    print("   c) 如果缓存中没有数据或数据不完整，从API获取")
    print("   d) API返回数据后，缓存结果供后续使用")
    print()
    
    print("3. 数据格式：")
    print("   - Price模型包含：open, close, high, low, volume, time")
    print("   - 时间格式：YYYY-MM-DD")
    print("   - 支持转换为pandas DataFrame进行分析")
    print()
    
    print("4. 主要函数：")
    print("   - get_prices(): 获取原始价格数据")
    print("   - get_price_data(): 获取价格数据并转换为DataFrame")
    print("   - get_current_price(): 获取特定日期的当前价格")
    print("   - calculate_price_momentum(): 计算价格动量指标")
    print()

def fetch_nvda_price_data(start_date: str, end_date: str):
    """获取NVDA价格数据"""
    print(f"=== 获取NVDA价格数据 ({start_date} 到 {end_date}) ===")
    
    try:
        # 使用项目中的价格获取函数
        prices = get_prices("NVDA", start_date, end_date)
        
        if not prices:
            print("❌ 未获取到价格数据")
            return None
            
        print(f"✅ 成功获取 {len(prices)} 条价格记录")
        
        # 转换为DataFrame
        df = pd.DataFrame([p.model_dump() for p in prices])
        df['Date'] = pd.to_datetime(df['time'])
        df.set_index('Date', inplace=True)
        df.sort_index(inplace=True)
        
        # 确保数值列为数值类型
        numeric_cols = ['open', 'close', 'high', 'low', 'volume']
        for col in numeric_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        print(f"📊 数据范围：{df.index.min().strftime('%Y-%m-%d')} 到 {df.index.max().strftime('%Y-%m-%d')}")
        print(f"💰 价格范围：${df['low'].min():.2f} - ${df['high'].max():.2f}")
        
        return df
        
    except Exception as e:
        print(f"❌ 获取价格数据失败：{e}")
        return None

def create_price_chart(df: pd.DataFrame, ticker: str = "NVDA"):
    """创建价格波动图"""
    print("=== 创建价格波动图 ===")
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12), height_ratios=[3, 1])
    
    # 主图：价格走势
    ax1.plot(df.index, df['close'], label='收盘价', color='#1f77b4', linewidth=2)
    ax1.fill_between(df.index, df['low'], df['high'], alpha=0.3, color='lightblue', label='日内波动范围')
    
    # 添加移动平均线
    df['MA5'] = df['close'].rolling(window=5).mean()
    df['MA20'] = df['close'].rolling(window=20).mean()
    
    ax1.plot(df.index, df['MA5'], label='5日均线', color='orange', linewidth=1, alpha=0.8)
    ax1.plot(df.index, df['MA20'], label='20日均线', color='red', linewidth=1, alpha=0.8)
    
    # 设置主图标题和标签
    ax1.set_title(f'{ticker} 股价走势图 (2025.01.01 - 2025.06.01)', fontsize=16, fontweight='bold')
    ax1.set_ylabel('价格 (USD)', fontsize=12)
    ax1.legend(loc='upper left')
    ax1.grid(True, alpha=0.3)
    
    # 格式化Y轴
    ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:.0f}'))
    
    # 子图：成交量
    ax2.bar(df.index, df['volume'], alpha=0.7, color='gray', label='成交量')
    ax2.set_ylabel('成交量', fontsize=12)
    ax2.set_xlabel('日期', fontsize=12)
    ax2.legend(loc='upper left')
    ax2.grid(True, alpha=0.3)
    
    # 格式化成交量Y轴
    ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x/1e6:.1f}M'))
    
    # 设置X轴日期格式
    for ax in [ax1, ax2]:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    chart_filename = f'{ticker}_price_chart_2025.png'
    plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
    print(f"📈 图表已保存为：{chart_filename}")
    
    # 显示图表
    plt.show()
    
    return fig

def calculate_statistics(df: pd.DataFrame, ticker: str = "NVDA"):
    """计算价格统计信息"""
    print(f"=== {ticker} 价格统计分析 ===")
    
    # 基本统计
    start_price = df['close'].iloc[0]
    end_price = df['close'].iloc[-1]
    total_return = (end_price - start_price) / start_price * 100
    
    max_price = df['high'].max()
    min_price = df['low'].min()
    avg_price = df['close'].mean()
    
    # 波动率计算
    df['daily_return'] = df['close'].pct_change()
    volatility = df['daily_return'].std() * np.sqrt(252) * 100  # 年化波动率
    
    # 最大回撤
    df['cumulative'] = (1 + df['daily_return']).cumprod()
    df['running_max'] = df['cumulative'].expanding().max()
    df['drawdown'] = (df['cumulative'] - df['running_max']) / df['running_max']
    max_drawdown = df['drawdown'].min() * 100
    
    print(f"📊 统计期间：{df.index.min().strftime('%Y-%m-%d')} 到 {df.index.max().strftime('%Y-%m-%d')}")
    print(f"💰 起始价格：${start_price:.2f}")
    print(f"💰 结束价格：${end_price:.2f}")
    print(f"📈 总收益率：{total_return:+.2f}%")
    print(f"🔺 最高价格：${max_price:.2f}")
    print(f"🔻 最低价格：${min_price:.2f}")
    print(f"📊 平均价格：${avg_price:.2f}")
    print(f"📊 年化波动率：{volatility:.2f}%")
    print(f"📉 最大回撤：{max_drawdown:.2f}%")
    print(f"📊 平均日成交量：{df['volume'].mean()/1e6:.1f}M")
    
    return {
        'start_price': start_price,
        'end_price': end_price,
        'total_return': total_return,
        'max_price': max_price,
        'min_price': min_price,
        'avg_price': avg_price,
        'volatility': volatility,
        'max_drawdown': max_drawdown,
        'avg_volume': df['volume'].mean()
    }

def main():
    """主函数"""
    print("🚀 NVDA价格数据分析和可视化")
    print("=" * 50)
    
    # 分析价格数据获取逻辑
    analyze_price_data_logic()
    
    # 设置日期范围
    start_date = "2025-01-01"
    end_date = "2025-06-01"
    
    # 获取价格数据
    df = fetch_nvda_price_data(start_date, end_date)
    
    if df is None or df.empty:
        print("❌ 无法获取价格数据，请检查：")
        print("   1. 网络连接是否正常")
        print("   2. FINANCIAL_DATASETS_API_KEY环境变量是否设置")
        print("   3. API配额是否充足")
        return
    
    # 计算统计信息
    stats = calculate_statistics(df, "NVDA")
    
    # 创建价格图表
    fig = create_price_chart(df, "NVDA")
    
    print("\n✅ 分析完成！")
    print(f"📈 图表文件：NVDA_price_chart_2025.png")

if __name__ == "__main__":
    main()
