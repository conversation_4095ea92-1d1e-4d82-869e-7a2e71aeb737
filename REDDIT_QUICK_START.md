# Reddit转储处理器快速入门指南

## 🚀 快速开始

### 1. 准备Reddit转储文件

将您下载的Reddit历史转储文件放在一个目录中，例如：
```
/path/to/reddit/dumps/
├── reddit_2025_01.jsonl
├── reddit_2025_02.jsonl.gz
├── reddit_2025_03.jsonl.bz2
└── ...
```

支持的文件格式：
- `.jsonl` - 标准JSONL格式
- `.json` - JSON格式
- `.jsonl.gz` - Gzip压缩的JSONL
- `.jsonl.bz2` - Bzip2压缩的JSONL
- `.jsonl.xz` - XZ压缩的JSONL

### 2. 运行处理器

```bash
# 基本使用 - 处理所有支持的股票
python reddit_dump_processor.py --dump-dir /path/to/reddit/dumps

# 指定特定股票和日期范围
python reddit_dump_processor.py \
  --dump-dir /path/to/reddit/dumps \
  --output-dir social_media_data \
  --start-date 2025-01-01 \
  --end-date 2025-06-01 \
  --tickers AAPL MSFT NVDA
```

### 3. 检查输出

处理完成后，您会在输出目录中看到：
```
social_media_data/
├── AAPL_social_media/
│   ├── reddit_2025-01-01.json
│   ├── reddit_2025-01-02.json
│   └── ...
├── MSFT_social_media/
├── NVDA_social_media/
└── ...
```

### 4. 与AI对冲基金系统集成

```bash
# 使用处理后的Reddit数据运行回测
python src/backtester.py \
  --tickers AAPL MSFT NVDA \
  --start-date 2025-01-01 \
  --end-date 2025-06-01 \
  --use_local_social_media
```

## 📊 处理统计示例

运行示例脚本查看处理效果：
```bash
python example_reddit_processing.py
```

示例输出：
```
处理完成统计:
处理文件数: 2
找到相关帖子数: 1392
按股票代码保存的帖子数:
  NVDA: 200
  MSFT: 200
  AAPL: 192
  ...
```

## 🔧 自定义配置

编辑 `reddit_dump_config.py` 来自定义：

### 添加新股票代码
```python
TICKER_TO_COMPANY = {
    'AAPL': ['Apple', 'AAPL', 'iPhone', 'iPad'],
    'YOUR_STOCK': ['Company Name', 'YOUR_STOCK', 'keyword1', 'keyword2'],
    # ...
}
```

### 添加监控子版块
```python
FINANCE_SUBREDDITS = {
    'investing', 'stocks', 'wallstreetbets',
    'your_custom_subreddit',
    # ...
}
```

### 修改情感关键词
```python
SENTIMENT_KEYWORDS = {
    'positive': ['bullish', 'buy', 'moon', 'rocket'],
    'negative': ['bearish', 'sell', 'crash', 'dump'],
    # ...
}
```

## 🧪 测试功能

运行测试确保一切正常：
```bash
python test_reddit_processor.py
```

预期输出：
```
✓ 所有测试通过！
Reddit转储处理器已准备就绪。
```

## 📈 数据质量

### 过滤标准
- ✅ 只处理金融相关子版块
- ✅ 内容必须包含股票代码或相关关键词
- ✅ 支持日期范围过滤
- ✅ 自动去重重复帖子

### 输出格式
每个JSON文件包含标准化的帖子数据：
```json
{
  "platform": "reddit",
  "post_id": "reddit_2025-01-01_abc123",
  "title": "AAPL earnings discussion",
  "content": "Apple earnings are amazing!",
  "author": "investor123",
  "created_time": "2025-01-01T09:30:00",
  "url": "https://reddit.com/r/stocks/comments/abc123",
  "upvotes": 250,
  "comments_count": 45,
  "ticker": "AAPL",
  "sentiment": "bullish",
  "engagement_score": 317.5,
  "source_subreddit": "stocks",
  "hashtags": null
}
```

## ⚡ 性能提示

### 大文件处理
- 处理器使用流式读取，可处理GB级文件
- 支持压缩文件直接处理
- 典型处理速度：10,000-50,000行/分钟

### 内存优化
- 按日期分批保存，避免内存积累
- 自动去重，避免重复数据
- 可通过配置调整批处理大小

## 🔍 故障排除

### 常见问题

**Q: 没有生成输出文件**
A: 检查日期范围、股票代码配置和转储文件内容

**Q: 处理速度很慢**
A: 压缩文件处理较慢，考虑先解压或调整批处理大小

**Q: 内存不足**
A: 减少配置文件中的batch_size设置

### 日志分析
查看 `reddit_dump_processor.log` 了解详细处理信息：
```
2025-01-01 10:00:00 - INFO - 找到 5 个转储文件
2025-01-01 10:01:00 - INFO - 已处理 10000 行，找到 150 个相关帖子
2025-01-01 10:02:00 - INFO - 保存 AAPL 2025-01-01: 25 个帖子
```

## 📚 更多资源

- 📖 [详细使用说明](REDDIT_DUMP_USAGE.md)
- 🔧 [配置文件说明](reddit_dump_config.py)
- 🧪 [测试脚本](test_reddit_processor.py)
- 💡 [示例脚本](example_reddit_processing.py)

## 🎯 下一步

1. **准备数据**: 下载Reddit历史转储文件
2. **运行处理**: 使用处理器提取股票相关数据
3. **集成系统**: 将处理后的数据用于AI对冲基金回测
4. **优化配置**: 根据需要调整股票代码和关键词

---

**准备好开始了吗？** 运行以下命令开始处理您的Reddit数据：

```bash
python reddit_dump_processor.py --dump-dir /path/to/your/reddit/dumps
```
