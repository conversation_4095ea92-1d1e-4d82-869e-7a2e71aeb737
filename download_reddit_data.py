#!/usr/bin/env python3
"""
Reddit数据下载器

跨平台的Reddit历史转储文件下载工具
支持断点续传和进度显示
"""

import os
import sys
import requests
from pathlib import Path
from typing import List, Optional
import argparse
from tqdm import tqdm

def download_file(url: str, filename: str, resume: bool = True) -> bool:
    """
    下载文件，支持断点续传和进度显示
    
    Args:
        url: 下载URL
        filename: 本地文件名
        resume: 是否支持断点续传
    
    Returns:
        下载是否成功
    """
    filepath = Path(filename)
    
    # 检查文件是否已存在
    if filepath.exists() and not resume:
        print(f"文件已存在: {filename}")
        return True
    
    # 获取已下载的文件大小
    resume_header = {}
    if resume and filepath.exists():
        resume_header = {'Range': f'bytes={filepath.stat().st_size}-'}
        print(f"续传下载: {filename} (已下载 {filepath.stat().st_size} 字节)")
    else:
        print(f"开始下载: {filename}")
    
    try:
        # 发送请求
        response = requests.get(url, headers=resume_header, stream=True)
        response.raise_for_status()
        
        # 获取文件总大小
        total_size = int(response.headers.get('content-length', 0))
        if resume and filepath.exists():
            total_size += filepath.stat().st_size
        
        # 打开文件进行写入
        mode = 'ab' if resume and filepath.exists() else 'wb'
        
        with open(filepath, mode) as f, tqdm(
            desc=filename,
            total=total_size,
            unit='B',
            unit_scale=True,
            unit_divisor=1024,
            initial=filepath.stat().st_size if resume and filepath.exists() else 0
        ) as pbar:
            
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    pbar.update(len(chunk))
        
        print(f"✓ 下载完成: {filename}")
        return True
        
    except requests.RequestException as e:
        print(f"✗ 下载失败: {filename} - {e}")
        return False
    except KeyboardInterrupt:
        print(f"\n下载被中断: {filename}")
        return False

def get_reddit_urls(months: List[str], include_comments: bool = False) -> List[tuple]:
    """
    生成Reddit数据下载URL列表
    
    Args:
        months: 月份列表，格式如 ['01', '02', '03']
        include_comments: 是否包含评论数据
    
    Returns:
        (URL, 文件名) 元组列表
    """
    base_url = "https://files.pushshift.io/reddit"
    urls = []
    
    # 提交数据
    for month in months:
        url = f"{base_url}/submissions/RS_2025-{month}.zst"
        filename = f"RS_2025-{month}.zst"
        urls.append((url, filename))
    
    # 评论数据（可选）
    if include_comments:
        for month in months:
            url = f"{base_url}/comments/RC_2025-{month}.zst"
            filename = f"RC_2025-{month}.zst"
            urls.append((url, filename))
    
    return urls

def estimate_download_size(months: List[str], include_comments: bool = False) -> str:
    """
    估算下载大小
    """
    # 每月提交数据约3GB，评论数据约15GB
    submission_size = len(months) * 3
    comment_size = len(months) * 15 if include_comments else 0
    total_gb = submission_size + comment_size
    
    return f"约 {total_gb} GB"

def main():
    parser = argparse.ArgumentParser(description='Reddit历史数据下载器')
    parser.add_argument('--months', nargs='+', 
                       default=['01', '02', '03', '04', '05', '06'],
                       help='要下载的月份 (默认: 01-06)')
    parser.add_argument('--include-comments', action='store_true',
                       help='包含评论数据 (文件很大)')
    parser.add_argument('--output-dir', default='reddit_dumps',
                       help='输出目录 (默认: reddit_dumps)')
    parser.add_argument('--no-resume', action='store_true',
                       help='禁用断点续传')
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print("=" * 60)
    print("Reddit历史数据下载器")
    print("=" * 60)
    
    # 显示下载信息
    months = args.months
    include_comments = args.include_comments
    estimated_size = estimate_download_size(months, include_comments)
    
    print(f"下载月份: {', '.join(months)}")
    print(f"包含评论: {'是' if include_comments else '否'}")
    print(f"估算大小: {estimated_size}")
    print(f"输出目录: {output_dir}")
    print(f"断点续传: {'否' if args.no_resume else '是'}")
    
    # 获取下载URL列表
    urls = get_reddit_urls(months, include_comments)
    print(f"文件数量: {len(urls)}")
    
    # 确认下载
    if len(urls) > 3:
        response = input(f"\n将下载 {len(urls)} 个文件，总大小 {estimated_size}。继续？ (y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("下载已取消")
            return
    
    print("\n开始下载...")
    
    # 切换到输出目录
    original_dir = os.getcwd()
    os.chdir(output_dir)
    
    try:
        # 下载文件
        successful_downloads = 0
        failed_downloads = 0
        
        for i, (url, filename) in enumerate(urls, 1):
            print(f"\n[{i}/{len(urls)}] 下载: {filename}")
            
            if download_file(url, filename, resume=not args.no_resume):
                successful_downloads += 1
            else:
                failed_downloads += 1
        
        # 显示结果
        print("\n" + "=" * 60)
        print("下载完成统计")
        print("=" * 60)
        print(f"成功下载: {successful_downloads} 个文件")
        print(f"下载失败: {failed_downloads} 个文件")
        
        if successful_downloads > 0:
            print(f"\n文件保存在: {output_dir.absolute()}")
            
            # 显示文件列表
            print("\n下载的文件:")
            for file in output_dir.glob("*.zst"):
                size_mb = file.stat().st_size / 1024 / 1024
                print(f"  {file.name}: {size_mb:.1f} MB")
            
            print("\n下一步操作:")
            print("1. 转换文件格式:")
            print(f"   python convert_reddit_dumps.py --input-dir {output_dir} --output-dir reddit_dumps_processed")
            print("\n2. 处理Reddit数据:")
            print("   python reddit_dump_processor.py --dump-dir reddit_dumps_processed")
            print("\n3. 运行AI对冲基金回测:")
            print("   python src/backtester.py --tickers AAPL MSFT NVDA --use_local_social_media")
        
        if failed_downloads > 0:
            print(f"\n{failed_downloads} 个文件下载失败，可以重新运行脚本续传")
    
    finally:
        # 恢复原始目录
        os.chdir(original_dir)

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n下载被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n下载过程中出错: {e}")
        sys.exit(1)
