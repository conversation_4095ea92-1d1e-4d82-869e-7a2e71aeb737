#!/usr/bin/env python3
"""
Reddit API故障排除工具
"""

import os
from dotenv import load_dotenv, set_key
from pathlib import Path

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("Reddit API故障排除工具")
    print("=" * 60)

def check_current_config():
    """检查当前配置"""
    print("\n🔍 当前配置:")
    load_dotenv()
    
    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    user_agent = os.getenv('REDDIT_USER_AGENT')
    username = os.getenv('REDDIT_USERNAME')
    password = os.getenv('REDDIT_PASSWORD')
    
    print(f"Client ID: {client_id[:8]}..." if client_id else "Client ID: 未设置")
    print(f"Client Secret: {'已设置' if client_secret else '未设置'}")
    print(f"User Agent: {user_agent}")
    print(f"用户名: {username if username else '未设置'}")
    print(f"密码: {'已设置' if password else '未设置'}")
    
    return client_id, client_secret, user_agent, username, password

def show_setup_guide():
    """显示设置指南"""
    print("\n📋 Reddit API设置指南:")
    print("=" * 40)
    print("1. 访问: https://www.reddit.com/prefs/apps")
    print("2. 点击 'Create App' 或 'Create Another App'")
    print("3. 填写应用信息:")
    print("   - Name: AI-Hedge-Fund-Bot (或任意名称)")
    print("   - App type: 选择 'script'")
    print("   - Description: AI hedge fund data collector")
    print("   - About URL: 留空")
    print("   - Redirect URI: http://localhost:8080")
    print("4. 点击 'Create app'")
    print("5. 记录以下信息:")
    print("   - Client ID: 应用名称下方的字符串")
    print("   - Client Secret: 'secret' 字段的值")
    print()

def interactive_setup():
    """交互式设置"""
    print("\n🔧 交互式配置:")
    print("请按照上述指南创建Reddit应用，然后输入以下信息:")
    print()
    
    client_id = input("Client ID: ").strip()
    client_secret = input("Client Secret: ").strip()
    
    print("\nUser Agent格式: AppName/Version by /u/YourUsername")
    user_agent = input("User Agent (回车使用默认): ").strip()
    if not user_agent:
        user_agent = "AI-Hedge-Fund-Bot/1.0 by /u/Available_Neck_1936"
    
    print("\n用户认证信息 (script类型应用需要):")
    username = input("Reddit用户名: ").strip()
    password = input("Reddit密码: ").strip()
    
    return client_id, client_secret, user_agent, username, password

def save_config(client_id, client_secret, user_agent, username, password):
    """保存配置"""
    print("\n💾 保存配置...")
    
    env_file = Path('.env')
    
    set_key(env_file, 'REDDIT_CLIENT_ID', client_id)
    set_key(env_file, 'REDDIT_CLIENT_SECRET', client_secret)
    set_key(env_file, 'REDDIT_USER_AGENT', user_agent)
    
    if username:
        set_key(env_file, 'REDDIT_USERNAME', username)
    if password:
        set_key(env_file, 'REDDIT_PASSWORD', password)
    
    print("✓ 配置已保存")

def test_new_config():
    """测试新配置"""
    print("\n🧪 测试新配置...")
    
    try:
        import subprocess
        result = subprocess.run([
            'python', 'test_reddit_connection.py'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ 配置测试成功！")
            return True
        else:
            print("❌ 配置测试失败:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def show_common_issues():
    """显示常见问题"""
    print("\n🚨 常见问题和解决方案:")
    print("=" * 40)
    print()
    print("1. invalid_grant 错误:")
    print("   - 检查Client ID和Secret是否正确")
    print("   - 确认应用类型为'script'")
    print("   - 检查用户名密码是否正确")
    print("   - 确认Reddit账户已验证邮箱")
    print()
    print("2. 403 Forbidden 错误:")
    print("   - 检查User Agent格式")
    print("   - 确认应用未被Reddit暂停")
    print("   - 尝试重新创建应用")
    print()
    print("3. 429 Too Many Requests:")
    print("   - 等待几分钟后重试")
    print("   - 检查是否有其他程序在使用API")
    print()
    print("4. User Agent 格式:")
    print("   - 正确: 'AppName/Version by /u/Username'")
    print("   - 错误: 'AppName' 或 'AppName/Version'")
    print()

def main():
    """主函数"""
    print_banner()
    
    # 检查当前配置
    current_config = check_current_config()
    
    # 显示设置指南
    show_setup_guide()
    
    # 显示常见问题
    show_common_issues()
    
    print("\n" + "=" * 60)
    choice = input("选择操作:\n1. 重新配置API\n2. 仅测试当前配置\n3. 退出\n请选择 (1-3): ").strip()
    
    if choice == '1':
        # 重新配置
        new_config = interactive_setup()
        save_config(*new_config)
        
        # 测试新配置
        if test_new_config():
            print("\n🎉 配置成功！现在可以开始收集Reddit数据了")
        else:
            print("\n❌ 配置仍有问题，请检查上述常见问题")
            
    elif choice == '2':
        # 仅测试
        if test_new_config():
            print("\n✅ 当前配置正常")
        else:
            print("\n❌ 当前配置有问题，建议重新配置")
            
    elif choice == '3':
        print("退出")
        return 0
    else:
        print("无效选择")
        return 1
    
    return 0

if __name__ == '__main__':
    try:
        exit(main())
    except KeyboardInterrupt:
        print("\n\n操作被用户中断")
        exit(1)
