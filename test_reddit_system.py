#!/usr/bin/env python3
"""
Reddit数据收集系统测试

测试Reddit实时数据收集系统的各个组件
"""

import os
import sys
import json
import tempfile
import unittest
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from reddit_live_collector import RedditLiveCollector, RedditConfig
from reddit_data_validator import RedditDataValidator

class TestRedditSystem(unittest.TestCase):
    """Reddit系统测试类"""
    
    def setUp(self):
        """测试设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = RedditConfig(
            client_id="test_client_id",
            client_secret="test_client_secret",
            user_agent="test_user_agent"
        )
    
    def tearDown(self):
        """测试清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_config_validation(self):
        """测试配置验证"""
        # 有效配置
        config = RedditConfig(
            client_id="valid_id",
            client_secret="valid_secret",
            user_agent="valid_agent"
        )
        self.assertEqual(config.client_id, "valid_id")
        self.assertEqual(config.client_secret, "valid_secret")
        self.assertEqual(config.user_agent, "valid_agent")
    
    @patch('reddit_live_collector.praw.Reddit')
    def test_collector_initialization(self, mock_reddit):
        """测试收集器初始化"""
        mock_reddit_instance = Mock()
        mock_reddit.return_value = mock_reddit_instance
        
        collector = RedditLiveCollector(self.config, self.temp_dir)
        
        self.assertEqual(collector.output_dir, Path(self.temp_dir))
        self.assertIsNotNone(collector.ticker_keywords)
        self.assertIsNotNone(collector.target_subreddits)
        self.assertIsNotNone(collector.sentiment_keywords)
    
    def test_ticker_extraction(self):
        """测试股票代码提取"""
        with patch('reddit_live_collector.praw.Reddit'):
            collector = RedditLiveCollector(self.config, self.temp_dir)
            
            # 测试文本中的股票提取
            text1 = "I think AAPL is going to the moon! Apple earnings are great."
            tickers1 = collector.extract_tickers_from_text(text1)
            self.assertIn('AAPL', tickers1)
            
            text2 = "Microsoft $MSFT and NVIDIA are my top picks"
            tickers2 = collector.extract_tickers_from_text(text2)
            self.assertIn('MSFT', tickers2)
            self.assertIn('NVDA', tickers2)
            
            # 测试空文本
            tickers3 = collector.extract_tickers_from_text("")
            self.assertEqual(tickers3, [])
    
    def test_sentiment_analysis(self):
        """测试情感分析"""
        with patch('reddit_live_collector.praw.Reddit'):
            collector = RedditLiveCollector(self.config, self.temp_dir)
            
            # 测试看涨情感
            bullish_text = "This stock is going to moon! Buy buy buy!"
            sentiment1 = collector.determine_sentiment(bullish_text)
            self.assertEqual(sentiment1, 'bullish')
            
            # 测试看跌情感
            bearish_text = "This stock will crash and dump hard. Sell everything!"
            sentiment2 = collector.determine_sentiment(bearish_text)
            self.assertEqual(sentiment2, 'bearish')
            
            # 测试中性情感
            neutral_text = "This is a normal discussion about the company."
            sentiment3 = collector.determine_sentiment(neutral_text)
            self.assertEqual(sentiment3, 'neutral')
    
    def test_engagement_score(self):
        """测试参与度分数计算"""
        with patch('reddit_live_collector.praw.Reddit'):
            collector = RedditLiveCollector(self.config, self.temp_dir)
            
            score1 = collector.calculate_engagement_score(100, 50)
            self.assertEqual(score1, 200.0)  # 100*1.0 + 50*2.0
            
            score2 = collector.calculate_engagement_score(0, 0)
            self.assertEqual(score2, 0.0)
    
    def create_sample_post_data(self):
        """创建示例帖子数据"""
        return {
            'platform': 'reddit',
            'post_id': 'reddit_test123',
            'title': 'Test AAPL Discussion',
            'content': 'Apple stock is looking bullish',
            'author': 'test_user',
            'created_time': '2025-01-01T10:00:00',
            'url': 'https://reddit.com/test',
            'upvotes': 100,
            'comments_count': 25,
            'ticker': 'AAPL',
            'sentiment': 'bullish',
            'engagement_score': 150.0,
            'source_subreddit': 'stocks',
            'hashtags': None
        }
    
    def test_data_saving(self):
        """测试数据保存功能"""
        with patch('reddit_live_collector.praw.Reddit'):
            collector = RedditLiveCollector(self.config, self.temp_dir)
            
            # 创建测试数据
            posts = [self.create_sample_post_data()]
            posts[0]['tickers'] = ['AAPL']  # 添加tickers字段用于保存
            
            # 保存数据
            collector.save_posts_by_date_and_ticker(posts)
            
            # 验证文件是否创建
            expected_file = Path(self.temp_dir) / "AAPL_social_media" / "reddit_2025-01-01.json"
            self.assertTrue(expected_file.exists())
            
            # 验证文件内容
            with open(expected_file, 'r') as f:
                saved_data = json.load(f)
            
            self.assertEqual(len(saved_data), 1)
            self.assertEqual(saved_data[0]['ticker'], 'AAPL')
            self.assertEqual(saved_data[0]['sentiment'], 'bullish')
    
    def test_data_validation(self):
        """测试数据验证功能"""
        # 创建测试数据文件
        test_dir = Path(self.temp_dir) / "test_data"
        test_dir.mkdir(parents=True)
        
        aapl_dir = test_dir / "AAPL_social_media"
        aapl_dir.mkdir(parents=True)
        
        # 创建有效的测试文件
        valid_data = [self.create_sample_post_data()]
        valid_file = aapl_dir / "reddit_2025-01-01.json"
        
        with open(valid_file, 'w') as f:
            json.dump(valid_data, f)
        
        # 创建无效的测试文件
        invalid_data = [{'invalid': 'data'}]
        invalid_file = aapl_dir / "reddit_2025-01-02.json"
        
        with open(invalid_file, 'w') as f:
            json.dump(invalid_data, f)
        
        # 运行验证
        validator = RedditDataValidator(str(test_dir))
        stats = validator.scan_data_directory()
        
        # 验证结果
        self.assertEqual(stats['total_files'], 2)
        self.assertEqual(stats['valid_files'], 1)
        self.assertEqual(stats['valid_posts'], 1)
        self.assertGreater(len(validator.errors), 0)  # 应该有错误
    
    def test_processed_posts_cache(self):
        """测试已处理帖子缓存"""
        with patch('reddit_live_collector.praw.Reddit'):
            collector = RedditLiveCollector(self.config, self.temp_dir)
            
            # 添加一些已处理的帖子ID
            test_ids = ['post1', 'post2', 'post3']
            collector.processed_posts.update(test_ids)
            
            # 保存缓存
            collector.save_processed_posts()
            
            # 创建新的收集器实例
            collector2 = RedditLiveCollector(self.config, self.temp_dir)
            
            # 验证缓存是否正确加载
            for post_id in test_ids:
                self.assertIn(post_id, collector2.processed_posts)
    
    @patch('reddit_live_collector.praw.Reddit')
    def test_mock_submission_processing(self, mock_reddit):
        """测试模拟提交处理"""
        # 创建模拟的Reddit提交
        mock_submission = Mock()
        mock_submission.id = 'test123'
        mock_submission.title = 'AAPL to the moon!'
        mock_submission.selftext = 'Apple stock is looking very bullish'
        mock_submission.author = Mock()
        mock_submission.author.__str__ = Mock(return_value='test_user')
        mock_submission.created_utc = datetime.now().timestamp()
        mock_submission.permalink = '/r/stocks/comments/test123'
        mock_submission.score = 150
        mock_submission.num_comments = 25
        mock_submission.subreddit = Mock()
        mock_submission.subreddit.display_name = 'stocks'
        
        collector = RedditLiveCollector(self.config, self.temp_dir)
        
        # 处理提交
        result = collector.process_submission(mock_submission)
        
        # 验证结果
        self.assertIsNotNone(result)
        self.assertEqual(result['post_id'], 'reddit_test123')
        self.assertIn('AAPL', result['tickers'])
        self.assertEqual(result['sentiment'], 'bullish')
        self.assertEqual(result['source_subreddit'], 'stocks')

class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def setUp(self):
        """设置集成测试"""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """清理集成测试"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_end_to_end_workflow(self):
        """测试端到端工作流程"""
        # 这个测试需要真实的Reddit API凭据，所以跳过
        self.skipTest("需要真实的Reddit API凭据")
    
    def test_data_format_compatibility(self):
        """测试数据格式兼容性"""
        # 创建符合AI对冲基金系统格式的测试数据
        test_data = [{
            'platform': 'reddit',
            'post_id': 'reddit_test123',
            'title': 'AAPL Discussion',
            'content': 'Apple earnings look good',
            'author': 'test_user',
            'created_time': '2025-01-01T10:00:00',
            'url': 'https://reddit.com/test',
            'upvotes': 100,
            'comments_count': 25,
            'ticker': 'AAPL',
            'sentiment': 'bullish',
            'engagement_score': 150.0,
            'source_subreddit': 'stocks',
            'hashtags': None
        }]
        
        # 保存测试数据
        test_dir = Path(self.temp_dir) / "AAPL_social_media"
        test_dir.mkdir(parents=True)
        
        test_file = test_dir / "reddit_2025-01-01.json"
        with open(test_file, 'w') as f:
            json.dump(test_data, f, indent=2)
        
        # 验证数据格式
        validator = RedditDataValidator(self.temp_dir)
        stats = validator.scan_data_directory()
        
        self.assertEqual(stats['valid_posts'], 1)
        self.assertEqual(len(validator.errors), 0)

def run_system_check():
    """运行系统检查"""
    print("=" * 60)
    print("Reddit数据收集系统检查")
    print("=" * 60)
    
    # 检查依赖包
    print("\n📦 检查依赖包...")
    required_packages = ['praw', 'python-dotenv', 'tqdm', 'schedule', 'pandas']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package}")
        except ImportError:
            print(f"❌ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  需要安装: pip install {' '.join(missing_packages)}")
        return False
    
    # 检查环境变量
    print("\n🔧 检查环境变量...")
    from dotenv import load_dotenv
    load_dotenv()
    
    reddit_vars = [
        'REDDIT_CLIENT_ID',
        'REDDIT_CLIENT_SECRET',
        'REDDIT_USER_AGENT'
    ]
    
    missing_vars = []
    for var in reddit_vars:
        if os.getenv(var):
            print(f"✓ {var}")
        else:
            print(f"❌ {var} (未设置)")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n⚠️  请运行: python setup_reddit_api.py")
        return False
    
    print("\n✅ 系统检查通过！")
    return True

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Reddit系统测试')
    parser.add_argument('--check', action='store_true',
                       help='运行系统检查')
    parser.add_argument('--unit-tests', action='store_true',
                       help='运行单元测试')
    parser.add_argument('--all', action='store_true',
                       help='运行所有测试')
    
    args = parser.parse_args()
    
    if args.check or args.all:
        if not run_system_check():
            return 1
    
    if args.unit_tests or args.all:
        print("\n🧪 运行单元测试...")
        
        # 运行单元测试
        loader = unittest.TestLoader()
        suite = unittest.TestSuite()
        
        # 添加测试类
        suite.addTests(loader.loadTestsFromTestCase(TestRedditSystem))
        suite.addTests(loader.loadTestsFromTestCase(TestIntegration))
        
        # 运行测试
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        if not result.wasSuccessful():
            return 1
    
    if not any([args.check, args.unit_tests, args.all]):
        parser.print_help()
    
    return 0

if __name__ == '__main__':
    exit(main())
