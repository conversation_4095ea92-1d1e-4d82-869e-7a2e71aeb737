#!/usr/bin/env python3
"""
Reddit数据收集定时任务

支持定期自动收集Reddit数据，适合长期运行
"""

import os
import sys
import time
import logging
import schedule
import argparse
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Optional

from reddit_live_collector import RedditLiveCollector, load_reddit_config

class RedditScheduler:
    """Reddit数据收集调度器"""
    
    def __init__(self, output_dir: str = "social_media_data",
                 tickers: Optional[List[str]] = None,
                 subreddits: Optional[List[str]] = None):
        """
        初始化调度器
        
        Args:
            output_dir: 输出目录
            tickers: 目标股票代码
            subreddits: 目标子版块
        """
        self.output_dir = output_dir
        self.tickers = tickers or ['AAPL', 'MSFT', 'NVDA', 'GOOGL', 'AMZN', 'TSLA']
        self.subreddits = subreddits
        
        # 设置日志
        self.setup_logging()
        
        # 初始化收集器
        try:
            config = load_reddit_config()
            self.collector = RedditLiveCollector(config, output_dir)
            self.logger.info("Reddit收集器初始化成功")
        except Exception as e:
            self.logger.error(f"初始化失败: {e}")
            raise
    
    def setup_logging(self):
        """设置日志"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 创建日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 文件日志
        file_handler = logging.FileHandler(
            log_dir / f"reddit_scheduler_{datetime.now().strftime('%Y%m%d')}.log"
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.INFO)
        
        # 控制台日志
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        console_handler.setLevel(logging.INFO)
        
        # 配置logger
        self.logger = logging.getLogger('RedditScheduler')
        self.logger.setLevel(logging.INFO)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def collect_daily_data(self):
        """收集每日数据"""
        try:
            self.logger.info("开始每日数据收集任务")
            
            # 收集昨天的数据
            end_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            start_date = end_date - timedelta(days=1)
            
            self.logger.info(f"收集时间范围: {start_date} 到 {end_date}")
            
            stats = self.collector.collect_data(
                start_date=start_date,
                end_date=end_date,
                tickers=self.tickers,
                subreddits=self.subreddits,
                limit_per_subreddit=500  # 每日任务使用较小的限制
            )
            
            self.logger.info(f"每日收集完成: {stats}")
            
        except Exception as e:
            self.logger.error(f"每日收集失败: {e}")
    
    def collect_hourly_data(self):
        """收集每小时数据"""
        try:
            self.logger.info("开始每小时数据收集任务")
            
            # 收集过去1小时的数据
            end_date = datetime.now()
            start_date = end_date - timedelta(hours=1)
            
            stats = self.collector.collect_data(
                start_date=start_date,
                end_date=end_date,
                tickers=self.tickers,
                subreddits=self.subreddits,
                limit_per_subreddit=100  # 每小时任务使用更小的限制
            )
            
            self.logger.info(f"每小时收集完成: {stats}")
            
        except Exception as e:
            self.logger.error(f"每小时收集失败: {e}")
    
    def collect_weekly_data(self):
        """收集每周数据（补充收集）"""
        try:
            self.logger.info("开始每周数据收集任务")
            
            # 收集过去7天的数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            
            stats = self.collector.collect_data(
                start_date=start_date,
                end_date=end_date,
                tickers=self.tickers,
                subreddits=self.subreddits,
                limit_per_subreddit=1000  # 每周任务使用较大的限制
            )
            
            self.logger.info(f"每周收集完成: {stats}")
            
        except Exception as e:
            self.logger.error(f"每周收集失败: {e}")
    
    def cleanup_old_logs(self):
        """清理旧日志文件"""
        try:
            log_dir = Path("logs")
            if not log_dir.exists():
                return
            
            # 删除30天前的日志
            cutoff_date = datetime.now() - timedelta(days=30)
            
            for log_file in log_dir.glob("reddit_scheduler_*.log"):
                try:
                    # 从文件名提取日期
                    date_str = log_file.stem.split('_')[-1]
                    file_date = datetime.strptime(date_str, '%Y%m%d')
                    
                    if file_date < cutoff_date:
                        log_file.unlink()
                        self.logger.info(f"删除旧日志: {log_file}")
                        
                except Exception as e:
                    self.logger.warning(f"处理日志文件失败 {log_file}: {e}")
            
        except Exception as e:
            self.logger.error(f"清理日志失败: {e}")
    
    def run_scheduler(self, mode: str = "daily"):
        """
        运行调度器
        
        Args:
            mode: 调度模式 ('hourly', 'daily', 'weekly', 'continuous')
        """
        self.logger.info(f"启动Reddit数据收集调度器 - 模式: {mode}")
        
        # 清除现有任务
        schedule.clear()
        
        if mode == "hourly":
            # 每小时收集
            schedule.every().hour.do(self.collect_hourly_data)
            self.logger.info("已设置每小时收集任务")
            
        elif mode == "daily":
            # 每天凌晨2点收集
            schedule.every().day.at("02:00").do(self.collect_daily_data)
            self.logger.info("已设置每日收集任务 (02:00)")
            
        elif mode == "weekly":
            # 每周日凌晨1点收集
            schedule.every().sunday.at("01:00").do(self.collect_weekly_data)
            self.logger.info("已设置每周收集任务 (周日 01:00)")
            
        elif mode == "continuous":
            # 连续模式：每小时 + 每日 + 每周
            schedule.every().hour.do(self.collect_hourly_data)
            schedule.every().day.at("02:00").do(self.collect_daily_data)
            schedule.every().sunday.at("01:00").do(self.collect_weekly_data)
            self.logger.info("已设置连续收集任务 (每小时 + 每日 + 每周)")
        
        # 每天凌晨3点清理日志
        schedule.every().day.at("03:00").do(self.cleanup_old_logs)
        
        # 立即执行一次收集（可选）
        if mode in ["daily", "weekly", "continuous"]:
            self.logger.info("执行初始数据收集...")
            self.collect_daily_data()
        
        # 主循环
        self.logger.info("调度器开始运行...")
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
                
        except KeyboardInterrupt:
            self.logger.info("调度器被用户中断")
        except Exception as e:
            self.logger.error(f"调度器运行错误: {e}")
            raise

def main():
    parser = argparse.ArgumentParser(description='Reddit数据收集定时任务')
    parser.add_argument('--mode', choices=['hourly', 'daily', 'weekly', 'continuous'],
                       default='daily', help='调度模式')
    parser.add_argument('--tickers', nargs='+',
                       default=['AAPL', 'MSFT', 'NVDA', 'GOOGL', 'AMZN', 'TSLA'],
                       help='目标股票代码')
    parser.add_argument('--subreddits', nargs='+',
                       help='目标子版块')
    parser.add_argument('--output-dir', default='social_media_data',
                       help='输出目录')
    parser.add_argument('--daemon', action='store_true',
                       help='后台运行模式')
    
    args = parser.parse_args()
    
    try:
        # 创建调度器
        scheduler = RedditScheduler(
            output_dir=args.output_dir,
            tickers=args.tickers,
            subreddits=args.subreddits
        )
        
        # 后台运行模式
        if args.daemon:
            import daemon
            import daemon.pidfile
            
            pid_file = Path("reddit_scheduler.pid")
            
            with daemon.DaemonContext(
                pidfile=daemon.pidfile.PIDLockFile(str(pid_file)),
                working_directory=os.getcwd()
            ):
                scheduler.run_scheduler(args.mode)
        else:
            # 前台运行
            scheduler.run_scheduler(args.mode)
        
    except KeyboardInterrupt:
        print("\n调度器被用户中断")
        return 0
    except Exception as e:
        print(f"调度器启动失败: {e}")
        return 1

if __name__ == '__main__':
    exit(main())
