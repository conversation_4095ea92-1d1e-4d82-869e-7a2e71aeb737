@echo off
REM Reddit Data Download Script for Windows
REM Download Reddit historical dump files for 2025

echo Starting Reddit data download...

REM Create download directory
if not exist reddit_dumps mkdir reddit_dumps
cd reddit_dumps

echo Downloading submission data...
echo Note: Each file is 2-5GB, total download ~15-30GB
echo This may take several hours depending on your internet speed

REM Download 2025 Reddit submission data (Jan-Jun)
REM Using PowerShell Invoke-WebRequest for Windows compatibility

powershell -Command "Invoke-WebRequest -Uri 'https://files.pushshift.io/reddit/submissions/RS_2025-01.zst' -OutFile 'RS_2025-01.zst' -Resume"
powershell -Command "Invoke-WebRequest -Uri 'https://files.pushshift.io/reddit/submissions/RS_2025-02.zst' -OutFile 'RS_2025-02.zst' -Resume"
powershell -Command "Invoke-WebRequest -Uri 'https://files.pushshift.io/reddit/submissions/RS_2025-03.zst' -OutFile 'RS_2025-03.zst' -Resume"
powershell -Command "Invoke-WebRequest -Uri 'https://files.pushshift.io/reddit/submissions/RS_2025-04.zst' -OutFile 'RS_2025-04.zst' -Resume"
powershell -Command "Invoke-WebRequest -Uri 'https://files.pushshift.io/reddit/submissions/RS_2025-05.zst' -OutFile 'RS_2025-05.zst' -Resume"
powershell -Command "Invoke-WebRequest -Uri 'https://files.pushshift.io/reddit/submissions/RS_2025-06.zst' -OutFile 'RS_2025-06.zst' -Resume"

echo Download completed!
echo Files downloaded:
dir *.zst

echo.
echo Next steps:
echo 1. Install zstd tool: Download from https://github.com/facebook/zstd/releases
echo 2. Convert files: python ..\convert_reddit_dumps.py --input-dir reddit_dumps --output-dir reddit_dumps_processed
echo 3. Process data: python ..\reddit_dump_processor.py --dump-dir reddit_dumps_processed
echo 4. Run backtesting: python ..\src\backtester.py --tickers AAPL MSFT NVDA --use_local_social_media

echo.
echo Important notes:
echo - The downloaded .zst files need to be converted to .jsonl format
echo - This will require additional disk space (3-5x the compressed size)
echo - Make sure you have zstd tool installed for file conversion

pause
